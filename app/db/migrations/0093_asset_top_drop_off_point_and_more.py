# Generated by Django 4.0.8 on 2025-07-10 04:36

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0092_zoomwebhooklog_zoomrecording_zoomaccount_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="asset",
            name="top_drop_off_point",
            field=models.IntegerField(
                default=0,
                help_text="Most common time point (in seconds) where viewers stop watching.",
                null=True,
                blank=True,
            ),
        ),
        migrations.AddField(
            model_name="asset",
            name="unique_completed_views_count",
            field=models.IntegerField(
                default=0,
                help_text="Number of unique visitors who watched video till the end.",
                null=True,
                blank=True,
            ),
        ),
        migrations.AlterField(
            model_name="asset",
            name="top_drop_off_point",
            field=models.PositiveBigIntegerField(
                default=0,
                help_text="Most common time point (in seconds) where viewers stop watching.",
                null=True,
                blank=True,
            ),
        ),
        migrations.AlterField(
            model_name="asset",
            name="unique_completed_views_count",
            field=models.PositiveBigIntegerField(
                default=0,
                help_text="Number of unique visitors who watched video till the end.",
                null=True,
                blank=True,
            ),
        ),
    ]
