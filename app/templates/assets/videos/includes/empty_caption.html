<div class="max-w-3xl" x-data="{
  showConfirmModal: false,
  showCreateCaptionModel: false,
  isGenerating: {% if asset.video.generate_subtitle and not has_auto_generated_subtitle %}true{% else %}false{% endif %},
  showAlert: false,
  alertType: 'info',
  alertMessage: '',
  showAlertMessage(type, message) {
    this.alertType = type;
    this.alertMessage = message;
    this.showAlert = true;
  }
}">
  <div class="relative" class="mt-2">
    <dl class="grid grid-cols-1 mt-6 xl:grid-cols-1 gap-y-4 w-full" x-show="isGenerating">
      {% include 'assets/videos/includes/generating_subtitle_alert.html' %}
    </dl>

    <div class="text-center py-10 flex flex-col items-center">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
        <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z" />
      </svg>
      <h3 class="mt-2 text-sm font-semibold text-gray-900">No Captions Uploaded Yet.</h3>
      <p class="mt-1 text-sm text-gray-500">Upload captions to make your videos accessible and engaging.</p>
      <div class="mt-6 flex items-center justify-center gap-x-4">

        <button type="button" @click="showCreateCaptionModel = !showCreateCaptionModel" class="inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-600">
          <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
          </svg>
          Add Caption
        </button>

        {% if not has_auto_generated_subtitle %}
          <div x-show="!isGenerating" class="mr-4">
            <button
              type="button"
              @click="showConfirmModal = true"
              class="inline-flex items-center rounded-md bg-blue-700 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
            >
              <svg class="-ml-0.5 mr-1.5 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
              </svg>
              Generate Auto Subtitle
            </button>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
  {% include 'assets/videos/includes/generate_subtitle_modal.html' %}
</div>
