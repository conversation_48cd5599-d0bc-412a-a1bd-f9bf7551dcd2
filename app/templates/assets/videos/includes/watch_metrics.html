{% load humanize %}
{% if asset.views_count%}
<div class="m-6 grid grid-cols-4 gap-4 lg:grid-cols-4 xl:gap-4">

  <div class="relative overflow-hidden rounded-xl border border-gray-200 bg-gradient-to-br from-blue-50 to-white p-3 shadow-sm sm:p-4">
    <div class="relative z-10">
      <div class="flex justify-between gap-x-3">
        <span class="mb-3 inline-flex h-8 w-8 items-center justify-center rounded-lg bg-white text-gray-700 shadow-sm md:h-10 md:w-10">
          <svg fill="none" stroke="currentColor" stroke-width="1.5" class="h-6 w-6 text-blue-500" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M15.91 11.672a.375.375 0 0 1 0 .656l-5.603 3.113a.375.375 0 0 1-.557-.328V8.887c0-.286.307-.466.557-.327l5.603 3.112z" />
          </svg>
        </span>
      </div>
      <h3 class="text-xs text-gray-800 md:text-sm">Times watched</h3>
      <div class="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between sm:gap-3">
        <h4 class="text-base font-semibold text-gray-800">{{ asset.views_count|intword }}</h4>
      </div>
    </div>
  </div>

  <div class="relative overflow-hidden rounded-xl border border-gray-200 bg-gradient-to-br from-purple-50 to-white p-3 shadow-sm sm:p-4">
    <div class="relative z-10">
      <div class="flex justify-between gap-x-3">
        <span class="mb-3 inline-flex h-8 w-8 items-center justify-center rounded-lg bg-white text-gray-700 shadow-sm md:h-10 md:w-10">
          <svg fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6 text-purple-500"><path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0z" /></svg>
        </span>
      </div>
      <h3 class="text-xs text-gray-800 md:text-sm">Unique Views</h3>
      <div class="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between sm:gap-3">
        <h4 class="text-base font-semibold text-gray-800">{{ asset.unique_viewers_count|intword }}</h4>
      </div>
    </div>
  </div>

  <div class="relative overflow-hidden rounded-xl border border-gray-200 bg-gradient-to-br from-emerald-50 to-white p-3 shadow-sm sm:p-4">
    <div class="relative z-10">
      <div class="flex justify-between gap-x-3">
        <span class="mb-3 inline-flex h-8 w-8 items-center justify-center rounded-lg bg-white text-gray-700 shadow-sm md:h-10 md:w-10">
          <svg class="h-6 w-6 text-emerald-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
            <circle cx="9" cy="7" r="4" />
            <polyline points="16 11 18 13 22 9" />
          </svg>
        </span>
      </div>
      <h3 class="text-xs text-gray-800 md:text-sm">View Completions</h3>
      <div class="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between sm:gap-3">
        <h4 class="text-base font-semibold text-gray-800">{{ asset.unique_completed_views_count|default:0|intword }}</h4>
      </div>
    </div>
  </div>

  <div class="relative overflow-hidden rounded-xl border border-gray-200 bg-gradient-to-br from-amber-50 to-white p-3 shadow-sm sm:p-4">
    <div class="relative z-10">
      <div class="flex justify-between gap-x-3">
        <span class="mb-3 inline-flex h-8 w-8 items-center justify-center rounded-lg bg-white text-gray-700 shadow-sm md:h-10 md:w-10">
          <svg fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-amber-500"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0z"/></svg>
        </span>
      </div>
      <h3 class="text-xs text-gray-800 md:text-sm">Total Watch Time</h3>
      <div class="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between sm:gap-3">
        <h4 class="text-base font-semibold text-gray-800">{{ asset.total_watch_time | humanize_time_compact }}</h4>
      </div>
    </div>
  </div>

  <div class="relative overflow-hidden rounded-xl border border-gray-200 bg-gradient-to-br from-cyan-50 to-white p-3 shadow-sm sm:p-4">
    <div class="relative z-10">
      <div class="flex justify-between gap-x-3">
        <span class="mb-3 inline-flex h-8 w-8 items-center justify-center rounded-lg bg-white text-gray-700 shadow-sm md:h-10 md:w-10">
          <svg fill="none" stroke="currentColor" stroke-width="1.5" class="w-6 h-6 text-cyan-400" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round"
          d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0z" />
      </svg>
        </span>
      </div>
      <h3 class="text-xs text-gray-800 md:text-sm">Average Watch Time</h3>
      <div class="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between sm:gap-3">
        <h4 class="text-base font-semibold text-gray-800">{{ asset.average_watched_time | humanize_time }}</h4>
      </div>
    </div>
  </div>

  <div class="relative overflow-hidden rounded-xl border border-gray-200 bg-gradient-to-br from-orange-50 to-white p-3 shadow-sm sm:p-4">
    <div class="relative z-10">
      <div class="flex justify-between gap-x-3">
        <span class="mb-3 inline-flex h-8 w-8 items-center justify-center rounded-lg bg-white text-gray-700 shadow-sm md:h-10 md:w-10">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-orange-500">
            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6 9 12.75l4.286-4.286a11.948 11.948 0 0 1 4.306 6.43l.776 2.898m0 0 3.182-5.511m-3.182 5.51-5.511-3.181" />
          </svg>

        </span>
      </div>
      <h3 class="text-xs text-gray-800 md:text-sm">Top Drop-off</h3>
      <div class="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between sm:gap-3">
        <h4 class="text-base font-semibold text-gray-800">{{ asset.top_drop_off_point|humanize_time|default:"N/A" }}</h4>
      </div>
    </div>
  </div>


  <div class="relative overflow-hidden rounded-xl border border-gray-200 bg-gradient-to-br from-lime-50 to-white p-3 shadow-sm sm:p-4">
    <div class="relative z-10">
      <div class="flex justify-between gap-x-3">
        <span class="mb-3 inline-flex h-8 w-8 items-center justify-center rounded-lg bg-white text-gray-700 shadow-sm md:h-10 md:w-10">
          <svg fill="none" stroke="currentColor" stroke-width="1.5" class="w-6 h-6 text-lime-500" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4 12h4l2-5 4 10 2-5h4" />
          </svg>
        </span>
      </div>
      <h3 class="text-xs text-gray-800 md:text-sm">Engagement Score</h3>

      <div class="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between sm:gap-3">
        <h4 class="text-base font-semibold text-gray-800">{{ asset.viewers_engagement_score }}%</h4>
          {% with score=asset.viewers_engagement_score %}
            {% if score >= 80 %}
              <div class="rounded-full px-2 py-0.5 text-xs font-medium bg-green-100 text-green-700">Excellent</div>
            {% elif score >= 60 %}
              <div class="rounded-full px-2 py-0.5 text-xs font-medium bg-yellow-100 text-yellow-700">Good</div>
            {% elif score >= 40 %}
              <div class="rounded-full px-2 py-0.5 text-xs font-medium bg-orange-100 text-orange-700">Average</div>
            {% elif score >= 20 %}
              <div class="rounded-full px-2 py-0.5 text-xs font-medium bg-red-100 text-red-500">Poor</div>
            {% else %}
              <div class="rounded-full px-2 py-0.5 text-xs font-medium bg-gray-100 text-gray-700">Very Poor</div>
            {% endif %}
          {% endwith %}
      </div>
    </div>
  </div>

  <div class="relative overflow-hidden rounded-xl border border-gray-200 bg-gradient-to-br from-red-50 to-white p-3 shadow-sm sm:p-4">
    <div class="relative z-10">
      <div class="flex justify-between gap-x-3">
        <span class="mb-3 inline-flex h-8 w-8 items-center justify-center rounded-lg bg-white text-gray-700 shadow-sm md:h-10 md:w-10">
          <svg fill="none" stroke="currentColor" stroke-width="1.5" class="w-6 h-6 text-red-500" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round"
          d="M12 9.75v6.75m0 0-3-3m3 3 3-3m-8.25 6a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75z" />
      </svg>
        </span>
      </div>
      <h3 class="text-xs text-gray-800 md:text-sm">Bandwidth Consumption</h3>
      <div class="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between sm:gap-3">
        <h4 class="text-base font-semibold text-gray-800">
          {% if asset.bytes and asset.views_count %}
            ≈ {{ asset.bytes|multiply:asset.views_count|filesizeformat }}
          {% else %}
            N/A
          {% endif %}
        </h4>
      </div>
    </div>
  </div>
</div>
{% else %}
  <div class="flex items-center justify-center w-full h-32 text-gray-500">
    <p class="text-xl font-semibold">This video hasn't been viewed yet.</p>
  </div>
{% endif %}
