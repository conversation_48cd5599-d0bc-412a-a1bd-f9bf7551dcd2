!function(global,factory){"object"==typeof exports&&"undefined"!=typeof module?module.exports=factory(require("video.js")):"function"==typeof define&&define.amd?define(["video.js"],factory):(global="undefined"!=typeof globalThis?globalThis:global||self).videojsSettingsMenu=factory(global.videojs)}(this,function(videojs){"use strict";var videojsModule=function(module){return module&&"object"==typeof module&&"default"in module?module:{default:module}}(videojs);function createModule(factory,path,module){return factory(module={path:path,exports:{},require:function(modulePath,relativePath){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(relativePath==null&&module.path)}},module.exports),module.exports}var assertThisInitialized=createModule(function(module){module.exports=function(self){if(void 0===self)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return self},module.exports.__esModule=!0,module.exports.default=module.exports}),setPrototypeOf=createModule(function(module){function setPrototypeOf(subClass,superClass){return module.exports=setPrototypeOf=Object.setPrototypeOf||function(subClass,superClass){return subClass.__proto__=superClass,subClass},module.exports.__esModule=!0,module.exports.default=module.exports,setPrototypeOf(subClass,superClass)}module.exports=setPrototypeOf,module.exports.__esModule=!0,module.exports.default=module.exports}),inherits=createModule(function(module){module.exports=function(subClass,superClass){subClass.prototype=Object.create(superClass.prototype),subClass.prototype.constructor=subClass,setPrototypeOf(subClass,superClass)},module.exports.__esModule=!0,module.exports.default=module.exports}),Plugin=videojsModule.default.getPlugin("plugin"),Component=videojsModule.default.getComponent("Component"),MenuButton=videojsModule.default.getComponent("MenuButton"),defaultOptions={items:["AudioTrackButton","SubsCapsButton","PlaybackRateMenuButton","RatesButton"],languages:{settings:"Settings",loading:"Loading",back:"Back",captions_off:"Captions Off",default_audio:"Default Audio",audio:"Audio",subtitles:"Subtitles",chapters:"Chapters",speed:"Speed",quality:"Quality"}},SettingsMenuPlugin=function(Plugin){function SettingsMenuPlugin(player,options){var self;self=Plugin.call(this,player)||this;var pluginInstance=assertThisInitialized(self);return pluginInstance.playerId=self.player.id(),self.options=videojsModule.default.mergeOptions(defaultOptions,options),self.player.ready(function(){self.player.addClass("vjs-settings-menu"),self.buildUI(),(videojsModule.default.browser.IS_IOS||videojsModule.default.browser.IS_ANDROID)&&self.mobileBuildUI()}),self.player.on("playbackRateSwitched",function(event){var targetPlayer=event.target.player.playbackRateSwitched;this.getChild("controlBar").getChild("settingsMenuButton").controlText(targetPlayer.height+"p, "+pluginInstance.formatBps(targetPlayer.bitrate))}),self.player.on("userinactive",function(){document.getElementById(pluginInstance.playerId).querySelectorAll(".vjs-menu").forEach(function(menuElement){menuElement.classList.remove("vjs-lock-open")})}),self.player.on("click",function(event){"VIDEO"===event.target.tagName&&document.getElementById(pluginInstance.playerId).querySelectorAll(".vjs-menu").forEach(function(menuElement){menuElement.classList.remove("vjs-lock-open")})}),self.player.on("loadstart",function(event){this.one("canplaythrough",function(event){pluginInstance.removeElementsByClass("vjs-setting-menu-clear"),videojsModule.default.browser.IS_IOS||videojsModule.default.browser.IS_ANDROID?pluginInstance.mobileBuildTopLevelMenu():pluginInstance.buildTopLevelMenu()})}),self}inherits(SettingsMenuPlugin,Plugin);var prototype=SettingsMenuPlugin.prototype;return prototype.buildUI=function(){var self=this,SettingsMenuButton=function(MenuButton){function SettingsMenuButton(player,options){var buttonInstance;(buttonInstance=MenuButton.call(this,player,options)||this).addClass("vjs-settings-menu"),buttonInstance.controlText(self.options.languages.loading);var buttonSelf=assertThisInitialized(buttonInstance);return self.player.one("canplaythrough",function(event){buttonSelf.controlText(self.options.languages.settings)}),buttonInstance.menu.contentEl_.id=self.playerId+"-vjs-settings-menu-default",buttonInstance}return inherits(SettingsMenuButton,MenuButton),SettingsMenuButton.prototype.handleClick=function(){videojsModule.default.browser.IS_IOS||videojsModule.default.browser.IS_ANDROID?self.player.getChild("settingsMenuMobileModal").el().style.display="block":(this.el().classList.toggle("vjs-toogle-btn"),this.menu.el().classList.toggle("vjs-lock-open"))},SettingsMenuButton}(MenuButton);videojsModule.default.registerComponent("settingsMenuButton",SettingsMenuButton),this.player.getChild("controlBar").addChild("settingsMenuButton"),this.player.getChild("controlBar").getChild("fullscreenToggle")&&this.player.getChild("controlBar").el().insertBefore(this.player.getChild("controlBar").getChild("settingsMenuButton").el(),this.player.getChild("controlBar").getChild("fullscreenToggle").el())},prototype.buildTopLevelMenu=function(){var self=this,settingsButton=self.player.getChild("controlBar").getChild("settingsMenuButton");settingsButton.menu;var menuContent=settingsButton.menu.contentEl_;menuContent.innerHTML="",menuContent.classList.add("vjs-sm-top-level");var headerElement=document.createElement("li");headerElement.className="vjs-sm-top-level-header";var headerText=document.createElement("span");headerText.innerHTML=self.options.languages.settings,headerElement.appendChild(headerText),menuContent.appendChild(headerElement);var hiddenItems=[],hasChapters=!1,hasSubtitles=!1;self.player.textTracks().tracks_&&self.player.textTracks().tracks_.forEach(function(track){"chapters"===track.kind&&(hasChapters=!0),"subtitles"!==track.kind&&"captions"!==track.kind||(hasSubtitles=!0)}),hasChapters||hiddenItems.push("ChaptersButton"),hasSubtitles||hiddenItems.push("SubsCapsButton"),self.options.items.filter(function(item){return!hiddenItems.includes(item)}).forEach(function(itemName){if(self.player.getChild("controlBar").getChild(itemName)){var initialStates=self.setInitialStates(itemName);self.player.getChild("controlBar").getChild(itemName).addClass("vjs-hide-settings-menu-item");var listItem=document.createElement("li");listItem.innerHTML=initialStates.language,listItem.setAttribute("data-component",itemName.toLowerCase()),listItem.className="vjs-sm-list-item";var arrowIcon=document.createElement("i");arrowIcon.className="setting-menu-list-arrow setting-menu-list-arrow-right",listItem.appendChild(arrowIcon);var defaultSpan=document.createElement("span");defaultSpan.id=self.playerId+"-setting-menu-child-span-"+itemName.toLowerCase(),defaultSpan.innerHTML=initialStates.default,listItem.appendChild(defaultSpan),menuContent.appendChild(listItem),setTimeout(function(){self.buildMenuList(itemName)},"ChaptersButton"===itemName?1e3:0)}});var listItems=document.querySelectorAll(".vjs-sm-list-item");Array.from(listItems).forEach(function(listItem){listItem.addEventListener("click",function(event){document.querySelectorAll(".vjs-sm-top-level").forEach(function(topLevel){topLevel.classList.add("vjs-hidden")});var childMenu=document.getElementById(self.playerId+"-setting-menu-child-menu-"+this.getAttribute("data-component"));childMenu.classList.remove("vjs-hidden"),childMenu.classList.add("vjs-lock"),event.preventDefault()})})},prototype.mobileBuildUI=function(){var MobileModal=function(Component){function MobileModal(player,options){return Component.call(this,player,options)||this}return inherits(MobileModal,Component),MobileModal.prototype.createEl=function(){return videojsModule.default.createEl("div",{className:"vjs-settings-menu-mobile"})},MobileModal}(Component);videojsModule.default.registerComponent("settingsMenuMobileModal",MobileModal),videojsModule.default.dom.prependTo(this.player.addChild("settingsMenuMobileModal").el(),document.body)},prototype.mobileBuildTopLevelMenu=function(){var self=this,player=this,mobileModal=this.player.getChild("settingsMenuMobileModal"),topLevelList=document.createElement("ul");topLevelList.className="vjs-sm-mob-top-level vjs-setting-menu-clear",mobileModal.el().appendChild(topLevelList);var mobileHeader=document.createElement("li");mobileHeader.className="vjs-setting-menu-mobile-top-header",mobileHeader.innerHTML=this.options.languages.settings,topLevelList.appendChild(mobileHeader);var hiddenItems=[],hasChapters=!1,hasSubtitles=!1;player.player.textTracks().tracks_&&player.player.textTracks().tracks_.forEach(function(track){"chapters"===track.kind&&(hasChapters=!0),"subtitles"!==track.kind&&"captions"!==track.kind||(hasSubtitles=!0)}),hasChapters||hiddenItems.push("ChaptersButton"),hasSubtitles||hiddenItems.push("SubsCapsButton"),player.options.items.filter(function(item){return!hiddenItems.includes(item)}).forEach(function(itemName){if(player.player.getChild("controlBar").getChild(itemName)){player.player.getChild("controlBar").getChild(itemName).addClass("vjs-hide-settings-menu-item");var initialStates=player.setInitialStates(itemName),mobileListItem=document.createElement("li");mobileListItem.setAttribute("data-component",itemName.toLowerCase()),mobileListItem.innerHTML=initialStates.language,mobileListItem.className="vjs-sm-top-level-item";var mobileDefaultSpan=document.createElement("span");mobileDefaultSpan.id=player.playerId+"-setting-menu-child-span-"+itemName.toLowerCase(),mobileDefaultSpan.innerHTML=initialStates.default,mobileListItem.appendChild(mobileDefaultSpan),topLevelList.appendChild(mobileListItem),setTimeout(function(){player.mobileBuildSecondLevelMenu(itemName,mobileModal.el())},"ChaptersButton"===itemName?1e3:0)}});var mobileListItems=document.querySelectorAll(".vjs-sm-top-level-item");Array.from(mobileListItems).forEach(function(mobileListItem){mobileListItem.addEventListener("click",function(event){event.preventDefault();var componentName=this.getAttribute("data-component");document.querySelectorAll(".vjs-sm-mob-top-level").forEach(function(topLevel){topLevel.classList.add("vjs-hidden")}),document.getElementById(player.playerId+"-mb-comp-"+componentName).classList.remove("vjs-hidden")})});var closeButton=document.createElement("li");closeButton.innerHTML="Close",closeButton.onclick=function(event){self.player.getChild("settingsMenuMobileModal").el().style.display="none"},closeButton.className="setting-menu-footer-default",topLevelList.appendChild(closeButton)},prototype.mobileBuildSecondLevelMenu=function(itemName,modalElement){var self=this;if(this.player.getChild("controlBar").getChild("settingsMenuButton"),this.player.getChild("controlBar").getChild(itemName)){for(var menuContent=this.player.getChild("controlBar").getChild(itemName).menu.contentEl_,childIndex=0;childIndex<menuContent.children.length;childIndex++){var childClass=menuContent.children[childIndex].getAttribute("class");"setting-menu-header"!==childClass&&"vjs-menu-title"!==childClass||menuContent.children[childIndex].remove()}menuContent.id=self.playerId+"-mb-comp-"+itemName.toLowerCase(),menuContent.classList.add("vjs-hidden"),menuContent.classList.add("vjs-sm-mob-second-level"),menuContent.classList.add("vjs-setting-menu-clear");var backHeader=document.createElement("li");backHeader.className="setting-menu-header",backHeader.setAttribute("data-component",itemName.toLowerCase());var backArrow=document.createElement("i");backArrow.className="setting-menu-list-arrow setting-menu-list-arrow-left",backHeader.appendChild(backArrow),backHeader.onclick=function(event){document.querySelectorAll(".vjs-sm-mob-top-level").forEach(function(topLevel){topLevel.classList.remove("vjs-hidden")}),document.querySelectorAll(".vjs-menu-content").forEach(function(menuContent){menuContent.classList.add("vjs-hidden")});var selectedItems=document.getElementById(self.playerId+"-mb-comp-"+this.getAttribute("data-component")).querySelectorAll(".vjs-selected");void 0!==selectedItems&&selectedItems.length>0&&selectedItems[0].textContent&&(document.getElementById(self.playerId+"-setting-menu-child-span-"+this.getAttribute("data-component")).innerText=self.cleanDefault(selectedItems[0].textContent)),document.querySelectorAll(".vjs-sm-list-item").forEach(function(listItem){listItem.classList.remove("vjs-hidden")}),document.querySelectorAll(".vjs-menu-content").forEach(function(menuContent){menuContent.classList.value.includes("vjs-lock")&&(menuContent.classList.remove("vjs-lock"),menuContent.classList.add("vjs-hidden"))})};var backText=document.createElement("span");backText.innerHTML=self.options.languages.back,backHeader.appendChild(backText),menuContent.insertBefore(backHeader,menuContent.firstChild),modalElement.appendChild(menuContent)}},prototype.buildMenuList=function(itemName){var self=this,settingsButton=this.player.getChild("controlBar").getChild("settingsMenuButton");if(this.player.getChild("controlBar").getChild(itemName)){for(var menuContent=this.player.getChild("controlBar").getChild(itemName).menu.contentEl_,childIndex=0;childIndex<menuContent.children.length;childIndex++){var childClass=menuContent.children[childIndex].getAttribute("class");"setting-menu-header"!==childClass&&"vjs-menu-title"!==childClass||menuContent.children[childIndex].remove()}menuContent.id=self.playerId+"-setting-menu-child-menu-"+itemName.toLowerCase(),menuContent.classList.add("vjs-hidden"),menuContent.classList.add("vjs-setting-menu-clear");var speedSliderContainer=null;if(itemName==="PlaybackRateMenuButton"){speedSliderContainer=self.addSpeedSlider(menuContent)}var backHeader=document.createElement("li");backHeader.className="setting-menu-header",backHeader.setAttribute("data-component",itemName.toLowerCase());var backArrow=document.createElement("i");backArrow.className="setting-menu-list-arrow setting-menu-list-arrow-left",backHeader.appendChild(backArrow),backHeader.onclick=function(event){var selectedItems=document.getElementById(self.playerId+"-setting-menu-child-menu-"+this.getAttribute("data-component")).querySelectorAll(".vjs-selected");if(void 0!==selectedItems&&selectedItems.length>0&&selectedItems[0].textContent){var selectedText=selectedItems[0].textContent;var displayText;if(selectedItems[0].classList.contains("vjs-speed-slider-container")){var speedValue=selectedItems[0].querySelector(".custom-speed-value");if(speedValue){var rate=parseFloat(speedValue.textContent);displayText="Custom ("+rate+")"}else{displayText="Custom"}}else{displayText=self.cleanDefault(selectedText)}document.getElementById(self.playerId+"-setting-menu-child-span-"+this.getAttribute("data-component")).innerText=displayText}document.querySelectorAll(".vjs-sm-top-level").forEach(function(topLevel){topLevel.classList.remove("vjs-hidden")}),document.querySelectorAll(".vjs-menu-content").forEach(function(menuContent){menuContent.classList.value.includes("vjs-lock")&&(menuContent.classList.remove("vjs-lock"),menuContent.classList.add("vjs-hidden"))})};var backText=document.createElement("span");backText.innerHTML=self.options.languages.back,backHeader.appendChild(backText),menuContent.insertBefore(backHeader,menuContent.firstChild),settingsButton.menu.el().appendChild(menuContent);if(speedSliderContainer){menuContent.addEventListener("click",function(event){if(event.target.matches(".vjs-menu-item:not(.vjs-speed-slider-container)")){speedSliderContainer.classList.remove("vjs-selected")}})}}},prototype.addSpeedSlider=function(menuContent){var self=this;var SPEED_MIN=.25;var SPEED_MAX=4;var SPEED_STEP=.05;var currentRate=this.player.playbackRate()||1;if(currentRate<SPEED_MIN)currentRate=SPEED_MIN;if(currentRate>SPEED_MAX)currentRate=SPEED_MAX;var sliderContainer=document.createElement("li");sliderContainer.className="vjs-menu-item vjs-speed-slider-container";var label=document.createElement("span");label.className="vjs-menu-item-text custom-speed-label";label.textContent="custom";var speedValue=document.createElement("span");speedValue.className="custom-speed-value";speedValue.textContent=currentRate.toFixed(2)+"x";speedValue.style.color="inherit";sliderContainer.appendChild(label);sliderContainer.appendChild(speedValue);var sliderWrapper=document.createElement("div");sliderWrapper.className="vjs-speed-slider-wrapper";var sliderInput=document.createElement("input");sliderInput.type="range";sliderInput.min=SPEED_MIN.toString();sliderInput.max=SPEED_MAX.toString();sliderInput.step=SPEED_STEP.toString();sliderInput.value=currentRate;sliderInput.className="vjs-speed-slider-input";function updateSliderFill(slider){var min=parseFloat(slider.min);var max=parseFloat(slider.max);var val=parseFloat(slider.value);var percent=(val-min)/(max-min)*100;slider.style.setProperty("--slider-value",percent+"%")}updateSliderFill(sliderInput);sliderWrapper.appendChild(sliderInput);sliderContainer.appendChild(sliderWrapper);sliderInput.addEventListener("input",function(){var newRate=parseFloat(this.value);speedValue.textContent=newRate.toFixed(2)+"x";updateSliderFill(this)});sliderInput.addEventListener("change",function(){var newRate=parseFloat(this.value);self.player.playbackRate(newRate);sliderContainer.classList.add("vjs-selected");menuContent.querySelectorAll(".vjs-menu-item:not(.vjs-speed-slider-container)").forEach(function(item){item.classList.remove("vjs-selected")})});menuContent.insertBefore(sliderContainer,menuContent.firstChild);return sliderContainer},prototype.setInitialStates=function(itemName){switch(itemName){case"RatesButton":return{default:"auto",language:this.options.languages.quality};case"PlaybackRateMenuButton":var currentRate=this.player.playbackRate();return{default:currentRate?`${currentRate}x`:"1x",language:this.options.languages.speed};case"ChaptersButton":return{default:"",language:this.options.languages.chapters};case"AudioTrackButton":for(var audioTracks=this.player.audioTracks(),defaultAudio=this.options.languages.defaultAudio,trackIndex=audioTracks.length;trackIndex--;)audioTracks[trackIndex].enabled&&(defaultAudio=audioTracks[trackIndex].label);return{default:defaultAudio,language:this.options.languages.audio};case"SubsCapsButton":for(var textTracks=this.player.textTracks(),captionsOff=this.options.languages.captions_off,trackIndex=textTracks.length;trackIndex--;)"subtitles"===textTracks[trackIndex].kind&&"showing"===textTracks[trackIndex].mode&&(captionsOff=textTracks[trackIndex].label);return{default:captionsOff,language:this.options.languages.subtitles};default:return{default:"",language:"Menu"}}},prototype.removeElementsByClass=function(className){document.querySelectorAll(".vjs-sm-top-level").forEach(function(topLevel){topLevel.classList.remove("vjs-hidden")});for(var elements=document.getElementsByClassName(className);elements.length>0;)elements[0].parentNode.removeChild(elements[0])},prototype.cleanDefault=function(text){var commaIndex=(text=text.replace(/\s\s+/g," ")).indexOf(",");return(text=text.substring(0,-1!=commaIndex?commaIndex:text.length)).replace(/(<([^>]+)>)/gi,"")},prototype.formatBps=function(bps){var unitIndex=-1;do{bps/=1024,unitIndex++}while(bps>1024);return Math.max(bps,.1).toFixed(1)+[" kbps"," Mbps"," Gbps"," Tbps","Pbps","Ebps","Zbps","Ybps"][unitIndex]},SettingsMenuPlugin}(Plugin);return SettingsMenuPlugin.defaultState={},SettingsMenuPlugin.VERSION="0.0.9",videojsModule.default.registerPlugin("settingsMenu",SettingsMenuPlugin),SettingsMenuPlugin});
