import json
from dataclasses import dataclass

import sentry_sdk
from django.conf import settings
from django.db import transaction
from django.db.models import Count, F, Sum
from django.utils import timezone
from user_agents import parse

from app.utils.browser import (
    get_client_from_user_agent,
    get_client_ip,
    get_device_from_user_agent,
    get_ip_location,
    get_parsed_browser_time,
    get_platform_from_user_agent,
)
from app.utils.wasabi import get_object_paths, read_file


@dataclass
class AnalyticsData:
    visitor_id: str = None
    session_id: str = None
    duration: int = 0


class RequestMetadata:
    def __init__(self, request):
        self.request = request

    @property
    def ip_address(self):
        return get_client_ip(self.request)

    @property
    def location(self):
        ip_address = self.ip_address
        return get_ip_location(ip_address)

    @property
    def user_agent(self):
        return self.request.META.get("HTTP_USER_AGENT", "")[:255]

    @property
    def device(self):
        user_agent = self.user_agent
        return get_device_from_user_agent(user_agent=user_agent)

    @property
    def platform(self):
        user_agent = self.user_agent
        return get_platform_from_user_agent(user_agent=user_agent)

    @property
    def client(self):
        user_agent = self.user_agent
        return get_client_from_user_agent(user_agent=user_agent)

    @property
    def browser_time(self):
        return get_parsed_browser_time(self.request)


@transaction.atomic
def track_analytics(request, asset, analytics_data):
    if is_analytics_data_not_valid(analytics_data):
        return

    asset_viewer_log = get_existing_asset_viewer_log(asset, analytics_data)

    if asset_viewer_log:
        update_duration_in_existing_asset_viewer_log(
            asset_viewer_log, analytics_data.duration
        )

    else:
        create_asset_viewer_log(request, asset, analytics_data)

    update_asset_watch_metrics(asset)


def is_analytics_data_not_valid(analytics_data):
    return (
        is_session_id_not_valid(analytics_data.session_id)
        or is_visitor_id_not_valid(analytics_data.visitor_id)
        or not analytics_data.duration
    )


def is_session_id_not_valid(session_id):
    return len(session_id) != 40


def is_visitor_id_not_valid(visitor_id):
    return len(visitor_id) != 32


def get_existing_asset_viewer_log(asset, analytics_data):
    from app.models.asset import AssetViewerLog

    return AssetViewerLog.objects.filter(
        asset=asset,
        session_id=analytics_data.session_id,
        visitor_id=analytics_data.visitor_id,
    ).first()


def update_duration_in_existing_asset_viewer_log(asset_viewer_log, duration):
    asset_viewer_log.duration = duration
    asset_viewer_log.save()


def create_asset_viewer_log(request, asset, analytics_data):
    from app.models.asset import AssetViewerLog

    request_metadata = RequestMetadata(request)
    AssetViewerLog.objects.create(
        asset=asset,
        organization=asset.organization,
        visitor_id=analytics_data.visitor_id,
        session_id=analytics_data.session_id,
        duration=analytics_data.duration,
        location=request_metadata.location,
        ip_address=request_metadata.ip_address,
        user_agent=request_metadata.user_agent,
        device=request_metadata.device,
        platform=request_metadata.platform,
        client=request_metadata.client,
    )

    asset.views_count = AssetViewerLog.objects.filter(asset=asset).count()
    asset.total_watch_time = AssetViewerLog.objects.filter(asset=asset).count()
    asset.save(update_fields=["views_count"])


def update_asset_watch_metrics(asset):
    asset.views_count = get_total_views(asset)
    asset.average_watched_time = calculate_average_watch_time(asset)
    asset.total_watch_time = calculate_total_watch_time(asset)
    asset.unique_viewers_count = calculate_unique_viewers_count(asset)
    asset.unique_completed_views_count = calculate_unique_completed_views_count(asset)
    asset.top_drop_off_point = calculate_top_drop_off_point(asset)
    asset.save(
        update_fields=[
            "views_count",
            "average_watched_time",
            "total_watch_time",
            "unique_viewers_count",
            "unique_completed_views_count",
            "top_drop_off_point",
        ]
    )


def get_total_views(asset):
    from app.models.asset import AssetViewerLog

    return AssetViewerLog.objects.filter(asset=asset).count()


def calculate_average_watch_time(asset):
    if not asset.views_count:
        return 0
    total_watch_time = calculate_total_watch_time(asset)
    return total_watch_time / asset.views_count


def calculate_total_watch_time(asset):
    from app.models.asset import Asset, AssetViewerLog

    if asset.type == Asset.Type.VIDEO:
        return (
            AssetViewerLog.objects.filter(asset=asset).aggregate(
                total_watch_time=Sum("duration")
            )["total_watch_time"]
            or 0
        )
    return 0


def calculate_unique_viewers_count(self):
    from app.models.asset import AssetViewerLog

    return (
        AssetViewerLog.objects.filter(asset=self)
        .values("visitor_id")
        .distinct()
        .count()
    )


def calculate_unique_completed_views_count(asset):
    from app.models.asset import AssetViewerLog

    if not hasattr(asset, "video") or not asset.video.duration:
        return 0

    video_duration_seconds = asset.video.duration.total_seconds()
    completion_threshold = video_duration_seconds * 0.9
    unique_completed_viewers = (
        AssetViewerLog.objects.filter(asset=asset, duration__gte=completion_threshold)
        .values("visitor_id")
        .distinct()
        .count()
    )

    return unique_completed_viewers


def calculate_top_drop_off_point(asset):
    from app.models.asset import AssetViewerLog

    if not hasattr(asset, "video") or not asset.video.duration:
        return None

    drop_off_data = (
        AssetViewerLog.objects.filter(asset=asset)
        .annotate(duration_rounded=((F("duration") / 30) * 30))
        .values("duration_rounded")
        .annotate(count=Count("id"))
        .order_by("-count", "duration_rounded")
        .first()
    )

    if not drop_off_data:
        return None

    return int(drop_off_data["duration_rounded"])


def get_metrics(asset, start_date, end_date):
    from app.models.asset import AssetViewerLog

    metrics = {}

    date_metrics = (
        AssetViewerLog.objects.filter(
            asset=asset,
            created__date__gte=start_date,
            created__date__lte=end_date,
        )
        .values("created__date")
        .annotate(
            total_watch_time=Sum("duration"),
            views_count=Count("id"),
        )
    )

    for entry in date_metrics:
        current_date = entry["created__date"]
        total_watch_time = entry["total_watch_time"] or 0
        views_count = entry["views_count"] or 0

        metrics[current_date] = {
            "total_watch_time": total_watch_time,
            "views_count": views_count,
        }

    return metrics


def total_users_using_ios():
    from app.models.asset import AssetViewerLog

    return AssetViewerLog.objects.filter(platform=AssetViewerLog.PLATFORM.IOS).count()


def total_users_using_android():
    from app.models.asset import AssetViewerLog

    return AssetViewerLog.objects.filter(
        platform=AssetViewerLog.PLATFORM.ANDROID
    ).count()


def total_users_using_web_platform():
    from app.models.asset import AssetViewerLog

    return AssetViewerLog.objects.filter(platform=AssetViewerLog.PLATFORM.WEB).count()


def total_users_watching_from_mobile():
    from app.models.asset import AssetViewerLog

    return AssetViewerLog.objects.filter(device=AssetViewerLog.DEVICE.MOBILE).count()


def total_users_using_system_device():
    from app.models.asset import AssetViewerLog

    return AssetViewerLog.objects.filter(device=AssetViewerLog.DEVICE.SYSTEM).count()


def total_users_using_tablet_device():
    from app.models.asset import AssetViewerLog

    return AssetViewerLog.objects.filter(device=AssetViewerLog.DEVICE.TABLET).count()


def total_users_using_ios_player_sdk():
    from app.models.asset import AssetViewerLog

    return AssetViewerLog.objects.filter(client=AssetViewerLog.CLIENT.IOS).count()


def total_users_using_android_player_sdk():
    from app.models.asset import AssetViewerLog

    return AssetViewerLog.objects.filter(client=AssetViewerLog.CLIENT.ANDROID).count()


def total_users_using_flutter_player_sdk():
    from app.models.asset import AssetViewerLog

    return AssetViewerLog.objects.filter(client=AssetViewerLog.CLIENT.FLUTTER).count()


def store_video_analytics_to_db(organization, date):
    next_batch_token = ""
    while True:
        (
            video_analytics_data,
            next_batch_token,
        ) = get_video_analytics_from_storage_in_batch(
            organization, date, next_batch_token
        )
        store_video_analytics(video_analytics_data, organization)
        if not next_batch_token:
            break
    update_watch_metrics_for_assets()


def get_video_analytics_from_storage_in_batch(
    organization, date, next_batch_token=None
):
    file_path = get_analytics_file_path_in_bucket(organization, date)
    try:
        analytics_data_paths, next_batch_token = get_object_paths(
            file_path, settings.ANALYTICS_LOG_BUCKET, next_batch_token
        )
        analytics_data = get_analytics_data_from_paths(analytics_data_paths)
        return analytics_data, next_batch_token
    except Exception as error:
        sentry_sdk.capture_exception(error)
        return


def get_analytics_file_path_in_bucket(organization, date):
    year, month, day = date.year, date.month, date.day
    return f"{organization.uuid}/{year}/{month:02d}/{day:02d}/"


def get_analytics_data_from_paths(analytics_data_paths):
    analytics_data = []
    for path in analytics_data_paths:
        content = read_file(path["Key"], bucket=settings.ANALYTICS_LOG_BUCKET)
        data = json.loads(content.decode("utf-8"))
        analytics_data.append(data)
    return analytics_data


def store_video_analytics(video_analytics_data, organization):
    from app.models.asset import AssetViewerLog

    if not video_analytics_data:
        return
    asset_viewer_logs = []
    for data in video_analytics_data:
        asset_viewer_log = create_asset_viewer_log_instance(data, organization)
        asset_viewer_logs.append(asset_viewer_log)
    AssetViewerLog.objects.bulk_create(asset_viewer_logs)


def create_asset_viewer_log_instance(data, organization):
    from app.models.asset import Asset, AssetViewerLog

    asset_id = data["assetId"]
    visitor_id = data["visitor_id"]
    session_id = data["session_id"]
    duration = int(data["duration"])
    user_agent = parse(data["user_agent"])
    asset_viewer_log = AssetViewerLog(
        asset=Asset.objects.get(uuid=asset_id),
        organization=organization,
        visitor_id=visitor_id,
        session_id=session_id,
        duration=duration,
        user_agent=user_agent,
    )
    return asset_viewer_log


def update_watch_metrics_for_assets():
    from app.models.asset import Asset, AssetViewerLog

    asset_to_update_watch_metrics = (
        AssetViewerLog.objects.filter(created__date=timezone.now().date())
        .values_list("asset__uuid", flat=True)
        .distinct()
    )
    for asset in Asset.objects.filter(uuid__in=asset_to_update_watch_metrics):
        update_asset_watch_metrics(asset)


def get_asset_views_analytics(asset, start_date=None, end_date=None):
    from app.models.asset import AssetViewerLog

    queryset = AssetViewerLog.objects.filter(asset=asset)
    if start_date and end_date:
        queryset = queryset.filter(
            created__date__gte=start_date, created__date__lte=end_date
        )

    data = list(
        queryset.values("location", "user_agent", "platform", "device", "client")
        .annotate(views=Count("id"))
        .order_by("-views")
    )

    return json.dumps(data)
