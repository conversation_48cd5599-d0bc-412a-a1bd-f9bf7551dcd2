import json
from datetime import timedelta

import requests
import sentry_sdk
from django.conf import settings
from django.db import transaction
from django.urls import reverse
from django.utils.crypto import get_random_string
from django.utils.timezone import now

from app.domain.aws import create_aws_live_stream_server
from app.domain.drm.encryption.streams import (
    generate_and_store_fairplay_encryption_keys,
)
from app.domain.haproxy import (
    add_ip_address_to_proxy,
    remove_assigned_ip_address_from_proxy,
)
from app.domain.linode import delete_linode_server
from app.domain.live_chat import disable_chat_room
from app.domain.openresty import (
    add_ip_address_to_digitalocean_openresty,
    add_ip_address_to_openresty,
    remove_ip_address_from_digitalocean_openresty,
    remove_ip_address_from_openresty,
)
from app.domain.organization import get_boto_client
from app.domain.video import create_video_input, start_transcoding
from app.models import (
    Asset,
    LiveStream,
    LiveStreamEvent,
    LiveStreamUsage,
    ScheduledTaskReference,
    Video,
)
from app.models.user import User
from app.tasks import ExportLiveChatTask
from app.tasks.live_chat import CreateChatRoomTask
from app.utils.datetime import should_start_instantly
from app.utils.digitalocean import (
    create_digital_ocean_live_stream_server,
    delete_digital_ocean_server,
)
from app.utils.duration import get_duration


@transaction.atomic
def create_live_stream(
    title,
    organization,
    enable_drm_for_recording,
    user,
    transcode_recorded_video=True,
    store_recorded_video=True,
    start=now(),
    enable_drm=False,
    enable_llhls=False,
    latency=LiveStream.Latency.NORMAL_LATENCY,
):
    live_stream = create_live_stream_asset(
        title,
        organization,
        enable_drm_for_recording,
        user,
        transcode_recorded_video,
        store_recorded_video,
        start=start,
        enable_drm=enable_drm,
        enable_llhls=enable_llhls,
        latency=latency,
    )
    if enable_drm:
        generate_and_store_fairplay_encryption_keys(
            live_stream.uuid.hex, live_stream.organization
        )

    if should_start_instantly(start):
        live_stream_server = create_remote_live_stream_server(live_stream.asset)
        store_live_stream_server_details(live_stream, live_stream_server)

    live_stream.asset.notify_webhook()
    return live_stream


def create_chat_room_task(live_stream):
    CreateChatRoomTask.apply_async(
        kwargs={
            "asset_id": live_stream.asset.uuid,
            "organization_uuid": live_stream.organization.uuid,
        }
    )


def create_live_stream_asset(
    title,
    organization,
    enable_drm_for_recording,
    user,
    transcode_recorded_video,
    store_recorded_video,
    start=None,
    enable_drm=False,
    enable_llhls=False,
    latency=LiveStream.Latency.NORMAL_LATENCY,
):
    if settings.DEBUG:
        title = "(local_live) " + title
    asset = Asset.objects.create(
        created_by=user,
        organization=organization,
        title=title,
        type=Asset.Type.LIVESTREAM,
    )

    resolutions = [
        LiveStream.Resolutions._240p.value,
        LiveStream.Resolutions._480p.value,
        LiveStream.Resolutions._720p.value,
    ]

    if organization.uuid == "n2nbhp":
        resolutions = [
            LiveStream.Resolutions._240p.value,
            LiveStream.Resolutions._480p.value,
            LiveStream.Resolutions._360p.value,
        ]
    if organization.uuid == "bg5yak":
        resolutions = [
            LiveStream.Resolutions._240p.value,
            LiveStream.Resolutions._360p.value,
            LiveStream.Resolutions._480p.value,
            LiveStream.Resolutions._720p.value,
        ]
    if organization.uuid in ["gbf6xz", "8ey4ag"]:
        resolutions = [
            LiveStream.Resolutions._720p.value,
        ]

    if organization.enable_hd_live_streaming:
        resolutions.append(LiveStream.Resolutions._1080p.value)

    live_stream = LiveStream.objects.create(
        asset=asset,
        organization=organization,
        transcode_recorded_video=transcode_recorded_video,
        store_recorded_video=store_recorded_video,
        server_ip=settings.LIVE_STREAM_IP,
        hls_url_path=f"live/{organization.uuid}/{asset.uuid}/video.m3u8",
        start=start,
        enable_drm_for_recording=enable_drm_for_recording,
        resolutions=resolutions,
        enable_drm=enable_drm,
        enable_llhls=enable_llhls,
        latency=latency,
    )
    return live_stream


def create_remote_live_stream_server(asset):
    if asset.live_stream.server_id:
        return
    truncated_title = asset.title[:232]
    server_name = f"{truncated_title}-org-{asset.organization.uuid}-{asset.uuid}"
    if asset.organization.uuid in settings.USE_DIGITALOCEAN_FOR_LIVE_STREAMING:
        live_stream_server = create_digital_ocean_live_stream_server(server_name, asset)
    elif settings.LIVE_STREAM_SERVER_PROVIDER == "aws":
        live_stream_server = create_aws_live_stream_server(
            server_name,
            asset,
        )
    else:
        live_stream_server = create_digital_ocean_live_stream_server(server_name, asset)
    return live_stream_server


def store_live_stream_server_details(live_stream, live_stream_server):
    if live_stream.server_id:
        return
    live_stream.server_status = live_stream_server.status
    live_stream.server_id = live_stream_server.id
    live_stream.server_private_ip = live_stream_server.private_ip_address
    live_stream.save()
    LiveStreamUsage.objects.create(
        server_ip=live_stream.server_ip,
        start_time=now(),
        cost_per_hour=live_stream_server.cost_per_hour,
        live_stream=live_stream,
        organization=live_stream.organization,
        server_provider=live_stream_server.provider,
    )


def update_live_stream_server_ip_address_to_proxy(live_stream, ip_address):
    update_rtmp_details_to_live_stream(live_stream, ip_address)
    if (
        live_stream.live_stream_usage.server_provider
        == LiveStreamUsage.ServerProvider.DIGITALOCEAN
    ):
        add_ip_address_to_digitalocean_openresty(ip_address, live_stream.asset)
    else:
        add_ip_address_to_openresty(live_stream.server_private_ip, live_stream.asset)
    add_ip_address_to_proxy(live_stream.server_private_ip, live_stream.asset)


def update_rtmp_details_to_live_stream(live_stream, ip_address):
    live_stream.server_ip = ip_address
    live_stream.server_status = LiveStream.ServerStatus.CREATED
    asset_id = live_stream.asset.uuid
    live_stream.rtmp_url = f"rtmp://{live_stream.server_ip}/live"
    live_stream.stream_key = (
        f"org-{live_stream.organization.uuid}-live-{asset_id}-{get_random_string(4)}"
    )
    live_stream.save()


def update_live_stream_status_as_available(live_stream):
    live_stream.server_status = LiveStream.ServerStatus.AVAILABLE
    live_stream.save()


def stop_live_stream(live_stream):
    revoke_live_stream_scheduled_server_deletion(live_stream)

    if live_stream.chat_room_id:
        disable_chat_room(live_stream.chat_room_id)
        ExportLiveChatTask.apply_async(
            kwargs={
                "asset_id": live_stream.asset.uuid,
                "organization_uuid": live_stream.organization.uuid,
            }
        )

    if live_stream.status == LiveStream.Status.NOT_STARTED:
        live_stream.status = LiveStream.Status.COMPLETED
        create_live_stream_event(
            live_stream,
            LiveStreamEvent.Type.RECORDING,
            {"message": "Stopping live stream"},
        )
        live_stream.save(update_fields=["status"])
        schedule_check_live_server_deletion(live_stream)
        delete_live_stream_server(live_stream)
        remove_ip_address_from_openresty(live_stream.asset)
        remove_assigned_ip_address_from_proxy(live_stream.asset)
        if (
            live_stream.live_stream_usage.server_provider
            == LiveStreamUsage.ServerProvider.DIGITALOCEAN
        ):
            remove_ip_address_from_digitalocean_openresty(live_stream.asset)
        return

    trigger_stop_to_liveflow(live_stream)


def revoke_live_stream_scheduled_server_deletion(live_stream):
    if live_stream.server_termination_task_id:
        ScheduledTaskReference.objects.revoke(
            task_id=live_stream.server_termination_task_id
        )


def is_live_stream_content_available(live_stream):
    from app.utils.browser import get_url_status_code

    video_url = (
        f"{live_stream.organization.cdn_url}live/"
        f"{live_stream.organization.uuid}/"
        f"{live_stream.asset.uuid}/video.mp4"
    )
    m3u8_url = (
        f"{live_stream.organization.cdn_url}live/"
        f"{live_stream.organization.uuid}/"
        f"{live_stream.asset.uuid}/video.m3u8"
    )

    if get_url_status_code(video_url) != 404 and get_url_status_code(m3u8_url) != 404:
        return True
    return False


def trigger_stop_to_liveflow(live_stream):
    output_path = f"wasabi://{live_stream.organization.bucket_name}/private/{live_stream.asset.uuid}/"
    url = f"http://{live_stream.server_ip}/stop_transcoding/"
    data = {"stream_key": live_stream.stream_key, "output_path": output_path}
    response = requests.post(
        url, data=json.dumps(data), headers={"content-type": "application/json"}
    )

    if response.status_code == 200:
        save_termination_details(live_stream)
    else:
        log_sentry_error(live_stream)


def save_termination_details(live_stream):
    with transaction.atomic():
        live_stream = LiveStream.objects.select_for_update().get(id=live_stream.id)
        live_stream.status = LiveStream.Status.STOPPED
        LiveStreamEvent.objects.create(
            type=LiveStreamEvent.Type.STOPPED,
            live_stream=live_stream,
            organization=live_stream.organization,
        )
        live_stream.end = now()
        live_stream.save(update_fields=["status", "end"])
    live_stream.asset.notify_webhook()


def log_sentry_error(live_stream):
    with sentry_sdk.push_scope() as scope:
        scope.set_tag("org_code", live_stream.organization.uuid)
        scope.set_extra("asset_id", live_stream.asset.uuid)
        scope.set_extra("server_ip", live_stream.server_ip)
        scope.set_extra("live_status", live_stream.get_status_display())
        scope.set_extra("server_status", live_stream.get_server_status_display())
        sentry_sdk.capture_message("Live Uploading Error")


@transaction.atomic
def transcode_live_stream(live_stream):
    update_live_stream_status(live_stream, LiveStream.Status.COMPLETED)
    live_stream.asset.notify_webhook()
    video = getattr(live_stream.asset, "video", None)
    if not video:
        video = create_video_for_live_stream(live_stream)
    if not video.inputs.exists():
        create_video_input(video)
    if is_transcoding_needed(live_stream):
        start_transcoding(video)
    else:
        video.status = Video.Status.COMPLETED
        video.save(update_fields=["status"])
    create_live_stream_event(
        live_stream,
        LiveStreamEvent.Type.RECORDING,
        {"message": "Transcoded live stream"},
    )


def is_transcoding_needed(live_stream):
    if not live_stream.transcode_recorded_video:
        return False

    if not live_stream.enable_drm and not live_stream.enable_drm_for_recording:
        return False

    return True


def update_live_stream_status(live_stream, status):
    live_stream.status = status
    live_stream.save()


def create_live_stream_event(live_stream, event_type, data=None):
    LiveStreamEvent.objects.create(
        type=event_type,
        live_stream=live_stream,
        organization=live_stream.organization,
        data=data or {},
    )


def create_video_for_live_stream(live_stream):
    content_protection_type = (
        Video.ContentProtectionType.DRM
        if live_stream.enable_drm_for_recording
        else Video.ContentProtectionType.DISABLED
    )
    resolutions = [
        Video.Resolutions._240p,
        Video.Resolutions._360p,
        Video.Resolutions._480p,
        Video.Resolutions._720p,
    ]
    if live_stream.organization.enable_hd_live_streaming:
        resolutions.append(Video.Resolutions._1080p)

    if live_stream.organization.uuid == "n2nbhp":
        resolutions = [
            Video.Resolutions._720p,
        ]
    from app.domain.video import create_video_with_outputs

    video = create_video_with_outputs(
        asset=live_stream.asset,
        organization=live_stream.organization,
        video_data={
            "video_codecs": [Video.VideoCodec.H264],
            "content_protection_type": content_protection_type,
            "resolutions": resolutions,
        },
    )
    if not is_transcoding_needed(live_stream):
        video.playback_url = f"transcoded/{live_stream.asset.uuid}/video.m3u8"
        video.save(update_fields=["playback_url"])

        duration = get_duration(video.get_playback_url())
        if duration > 0:
            video.duration = timedelta(seconds=round(duration))
            video.save(update_fields=["duration"])
    return video


def delete_live_stream_server(live_stream):
    from app.domain.aws import delete_aws_server

    if is_server_deleted(live_stream):
        return
    update_live_stream_server_status(live_stream, LiveStream.ServerStatus.DELETING)

    if (
        live_stream.live_stream_usage.server_provider
        == LiveStreamUsage.ServerProvider.DIGITALOCEAN
    ):
        delete_digital_ocean_server(live_stream.server_id)
    elif (
        live_stream.live_stream_usage.server_provider
        == LiveStreamUsage.ServerProvider.LINODE
    ):
        delete_linode_server(live_stream.server_id)
    else:
        delete_aws_server(live_stream.server_id)

    create_live_stream_event(
        live_stream,
        LiveStreamEvent.Type.RECORDING,
        {"message": "Live stream server deleted"},
    )
    update_live_stream_usage(live_stream)
    update_live_stream_server_status(live_stream, LiveStream.ServerStatus.DESTROYED)


def is_server_deleted(live_stream):
    return live_stream.server_status == LiveStream.ServerStatus.DESTROYED


def update_live_stream_server_status(live_stream, server_status):
    live_stream.server_status = server_status
    live_stream.save(update_fields=["server_status"])


def update_live_stream_usage(live_stream):
    live_stream_usage = live_stream.live_stream_usage
    live_stream_usage.end_time = now()
    live_stream_usage.save(update_fields=["end_time"])


def schedule_check_live_server_deletion(live_stream):
    from app.tasks.live_stream import CheckLiveServerDeletionTask

    CheckLiveServerDeletionTask.schedule(
        run_at=now() + timedelta(hours=1),
        kwargs={
            "live_stream_id": live_stream.id,
            "organization_uuid": live_stream.organization.uuid,  # type: ignore
        },
    )


def update_termination_log(live_stream, request):
    from app.utils.browser import get_user_details

    live_stream.termination_cause = LiveStream.TerminationCause.USER_INITIATED
    live_stream.user_details = get_user_details(request)
    live_stream.save(update_fields=["termination_cause", "user_details"])


def get_live_stream_drm_data(live_stream):
    if not live_stream.enable_drm:
        return {}

    widevine_license_url = settings.LICENSE_SERVER_URL + reverse(
        "api:generate-widevine-key",
        kwargs={
            "organization_id": live_stream.organization.uuid,
        },
    )

    fairplay_key_data = generate_and_store_fairplay_encryption_keys(
        live_stream.uuid.hex, live_stream.organization
    )

    return {
        "fairplay": {
            "key": fairplay_key_data["key"],
            "uri": fairplay_key_data["uri"],
            "iv": fairplay_key_data["iv"],
            "content_id": live_stream.uuid.hex,
        },
        "widevine": {
            "signer": settings.DRM_SIGNER,
            "content_id": live_stream.uuid.hex,
            "aes_signing_iv": live_stream.organization.drm_aes_signing_iv,
            "key_server_url": widevine_license_url,
            "aes_signing_key": live_stream.organization.drm_aes_signing_key,
        },
    }


def calculate_total_stream_duration(live_stream):
    events = live_stream.events.order_by("created")
    total_stream_time = timedelta()
    start_time = None
    buffer_time = 40
    for event in events:
        buffer_time += 15
        if event.get_type_display() == "On Pubish":
            start_time = event.created
        elif event.get_type_display() == "On Pubish Done" and start_time is not None:
            stop_time = event.created
            duration = stop_time - start_time
            total_stream_time += duration
            start_time = None

    if start_time is not None and live_stream.end is not None:
        stop_time = live_stream.end
        duration = stop_time - start_time
        total_stream_time += duration

    if total_stream_time > timedelta(0):
        return int(total_stream_time.total_seconds() - buffer_time)

    return 0


def verify_and_delete_server(live_stream):
    video_url = get_live_source_video_url(live_stream)
    if get_duration(video_url) > calculate_total_stream_duration(live_stream):
        delete_live_stream_server(live_stream)
        remove_ip_address_from_openresty(live_stream.asset)
        remove_assigned_ip_address_from_proxy(live_stream.asset)
        create_live_stream_event(
            live_stream,
            LiveStreamEvent.Type.COMPLETED,
            {"message": "Verified and Deleted live stream server"},
        )
    else:
        with sentry_sdk.push_scope() as scope:
            scope.set_tag("org_code", live_stream.organization.uuid)
            scope.set_extra("asset_id", live_stream.asset.uuid)
            scope.set_extra(
                "streamed_duration", calculate_total_stream_duration(live_stream)
            )
            scope.set_extra("saved_video_duration", get_duration(video_url))
            scope.set_extra("asset_id", live_stream.asset.uuid)
            scope.set_extra("Is duration mismatch error", True)
            sentry_sdk.capture_message("Duration Mismatch Error")
        create_live_stream_event(
            live_stream,
            LiveStreamEvent.Type.RECORDING,
            {"message": "Duration mismatch error while verifying live stream server"},
        )


def get_live_source_video_url(live_stream):
    return get_boto_client(live_stream.organization).generate_presigned_url(
        "get_object",
        Params={
            "Bucket": live_stream.organization.bucket_name,
            "Key": f"private/{live_stream.asset.uuid}/video.mp4",
        },
    )


def update_live_stream_server_status_as_creating(live_stream):
    live_stream.server_status = LiveStream.ServerStatus.CREATING
    live_stream.save(update_fields=["server_status"])


def terminate_live_server_on_transcoding_completion(asset):
    if hasattr(asset, "live_stream") and asset.video.status == Video.Status.COMPLETED:
        try:
            execute_queued_video_trim(asset)
        except Exception as e:
            sentry_sdk.capture_exception(e)
        schedule_check_live_server_deletion(asset.live_stream)
        delete_live_stream_server(asset.live_stream)
        remove_ip_address_from_openresty(asset)
        remove_assigned_ip_address_from_proxy(asset)


def execute_queued_video_trim(asset):
    trim_data = asset.live_stream.scheduled_trim_data or {}

    start_time = trim_data.get("start_time")
    end_time = trim_data.get("end_time")
    user_id = trim_data.get("user_id")

    if start_time is not None or end_time is not None:
        try:
            from app.domain.video_trim.core import start_video_trim_background_task

            try:
                user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                sentry_sdk.capture_message(
                    f"User {user_id} not found for scheduled trim on video {asset.video.id}",
                    level="warning",
                )
                return
            start_video_trim_background_task(asset.video, start_time, end_time, user)

        except Exception as e:
            sentry_sdk.capture_exception(e)


def upload_live_stream_recording(live_stream):
    from app.tasks.live_stream import UploadLiveStreamRecordingTask

    upload_task = UploadLiveStreamRecordingTask.apply_async(
        kwargs={
            "live_stream_id": live_stream.id,
            "organization_uuid": live_stream.organization.uuid,  # type: ignore
        },
        queue="live_stream_recording_upload",
    )
    live_stream.upload_recording_task_id = upload_task.task_id
    live_stream.save(update_fields=["upload_recording_task_id"])


def schedule_files_deletion(live_stream):
    from app.tasks.live_stream import DeleteLiveStreamFilesTask

    DeleteLiveStreamFilesTask.schedule(
        run_at=now() + timedelta(days=1),
        kwargs={
            "live_stream_id": live_stream.id,
            "organization_uuid": live_stream.organization.uuid,  # type: ignore
        },
        queue="live_stream",
    )


def schedule_stop_disconnected_live_stream(live_stream):
    from app.tasks.live_stream import StopDisconnectedLiveStreamTask

    live_stream_stop_schedule_time = 60  # After 60 minutes stop livestream
    if live_stream.organization.uuid in ["bg5yak"]:
        live_stream_stop_schedule_time = 30

    task = StopDisconnectedLiveStreamTask.schedule(
        run_at=now() + timedelta(minutes=live_stream_stop_schedule_time),
        kwargs={"organization_uuid": live_stream.organization.uuid, "live_stream_id": live_stream.id},  # type: ignore
        queue="live_stream",
    )
    live_stream.server_termination_task_id = task.task_id
    live_stream.save(update_fields=["server_termination_task_id"])
