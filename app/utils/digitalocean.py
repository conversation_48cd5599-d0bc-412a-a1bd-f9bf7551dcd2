import json
import re

import requests
from django.conf import settings
from django.urls import reverse

from app.utils.live_stream import LiveStreamServer

DROPLET_SIZE = "s-1vcpu-1gb"
DROPLET_REGION = "blr1"

headers = {
    "Authorization": f"Bearer {settings.DIGITAL_OCEAN_API_KEY}",
    "Content-Type": "application/json",
}


def sanitize_droplet_name(name):
    sanitized = re.sub(r"[^a-z0-9-]", "-", str(name).lower())
    sanitized = re.sub(r"-+", "-", sanitized)
    sanitized = sanitized.strip("-")

    if not sanitized[0].isalpha():
        sanitized = "d-" + sanitized

    if len(sanitized) < 3:
        sanitized = sanitized + "-do"
    elif len(sanitized) > 63:
        sanitized = sanitized[:63]

    return sanitized


def create_digital_ocean_live_stream_server(name, asset, size=DROPLET_SIZE):
    from app.domain.live_stream import is_transcoding_needed

    output_path = get_output_path(asset)
    store_backup_in_aws = is_transcoding_needed(asset.live_stream)

    organization = asset.organization
    url = "https://api.digitalocean.com/v2/droplets"
    sanitized_name = sanitize_droplet_name(name)

    server_type = (
        "s-4vcpu-8gb" if organization.enable_hd_live_streaming else "s-2vcpu-4gb"
    )
    if asset.live_stream.enable_drm:
        server_type = "s-4vcpu-8gb"

    data = {
        "name": sanitized_name,
        "size": server_type,
        "region": "blr1",
        "image": "192795106",
        "vpc_uuid": "8ac2bfa9-42b7-4f9e-a7d4-80ac47fdc83e",
        "user_data": get_user_data(
            asset, output_path, organization, store_backup_in_aws
        ),
        "with_droplet_agent": False,
    }

    response = requests.post(url, data=json.dumps(data), headers=headers)
    response.raise_for_status()
    return _parse_live_stream_server_response(response.json())


def get_output_path(asset):
    from app.domain.live_stream import is_transcoding_needed

    organization = asset.organization
    output_path = f"s3://{asset.organization.bucket_name}/transcoded/{asset.uuid}/"

    requires_transcoding = is_transcoding_needed(asset.live_stream)
    if requires_transcoding:
        output_path = f"s3://{settings.LIVE_STREAM_S3_BUCKET}/live_streams/{organization.uuid}/{asset.uuid}/"

    return output_path


def get_user_data(asset, output_path, organization, store_backup_in_aws):
    callback_url = settings.SITE_URL + reverse(
        "api:live-stream-server-callback",
        kwargs={
            "organization_id": organization.uuid,
        },
    )

    aws_access_key = settings.TPSTREAMS_AWS_ACCESS_KEY_ID
    aws_secret_key = settings.TPSTREAMS_AWS_SECRET_ACCESS_KEY
    access_key = organization.storage_access_key_id
    secret_key = organization.storage_secret_access_key
    region = organization.storage_region
    endpoint = (
        f"https://s3.{region}.amazonaws.com"
        if organization.get_storage_vendor_display() == "Aws"
        else f"https://s3.{region}.wasabisys.com"
    )
    provider = "AWS" if organization.get_storage_vendor_display() == "Aws" else "Wasabi"
    upload_to_S3_script = f"""
      - path: /home/<USER>/rclone.sh
        permissions: '0755'
        owner: root
        content: |
          #!/bin/bash

          rclone copy /home/<USER>/livestream/ {output_path} --config=/home/<USER>/.s3_config --exclude "/video.mp4" --exclude "*.txt"
    """

    return f"""
    #cloud-config
    apt_update: false
    apt_upgrade: false
    package_update: false
    package_upgrade: false
    package_reboot_if_required: false

    write_files:
      - path: /tmp/post_data.sh
        permissions: '0755'
        owner: root
        content: |
          #!/bin/bash
          SERVER_ID=$(curl -s http://***************/metadata/v1/id)
          IP_ADDRESS=$(curl -s http://***************/metadata/v1/interfaces/public/0/ipv4/address)
          URL={callback_url}

          JSON_DATA=$(cat <<EOF
          {{
            "server_id": "$SERVER_ID",
            "ip_address": "$IP_ADDRESS",
            "status": "created"
          }}
          EOF
          )

          curl -X POST -H "Content-Type: application/json" -d "$JSON_DATA" "$URL"

      - path: /etc/environment
        permissions: '0644'
        owner: root
        content: |
          SERVER_ID=$SERVER_ID
          ORG_CODE={organization.uuid}
          WASABI_ACCESS_KEY_ID={organization.storage_access_key_id}
          WASABI_BUCKET_REGION={organization.storage_region}
          WASABI_SECRET_ACCESS_KEY={organization.storage_secret_access_key}
          TPSTREAMS_URL={settings.SITE_URL}

      - path: /home/<USER>/.s3_config
        permissions: '0755'
        owner: root
        content: |
          [s3]
          type = s3
          provider = {'AWS' if store_backup_in_aws else provider}
          access_key_id = {aws_access_key if store_backup_in_aws else access_key}
          secret_access_key = {aws_secret_key if store_backup_in_aws else secret_key}
          storage_class = STANDARD
          acl = public-read
          {'region = '+ region if not store_backup_in_aws else ''}
          {'endpoint = '+ endpoint if not store_backup_in_aws else ""}
      {upload_to_S3_script}


    runcmd:
      - SERVER_ID=$(curl -s http://***************/metadata/v1/id)
      - /tmp/post_data.sh
      - source /etc/environment
      - cd /home/<USER>/workspace/liveflow/live-flow/
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow pull origin main
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow fetch
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow checkout debug
      - sed -i 's|^environment=.*|&SERVER_ID="'"${{SERVER_ID}}"'", ORG_CODE="{organization.uuid}", TPSTREAMS_URL="{settings.SITE_URL}", WASABI_ACCESS_KEY_ID="{organization.storage_access_key_id}", WASABI_BUCKET_REGION="{organization.storage_region}", WASABI_SECRET_ACCESS_KEY="{organization.storage_secret_access_key}"|' /etc/supervisor/conf.d/celeryd.conf
      - sed -i 's|^environment=.*|&SERVER_ID="'"${{SERVER_ID}}"'", ORG_CODE="{organization.uuid}", TPSTREAMS_URL="{settings.SITE_URL}", WASABI_ACCESS_KEY_ID="{organization.storage_access_key_id}", WASABI_BUCKET_REGION="{organization.storage_region}", WASABI_SECRET_ACCESS_KEY="{organization.storage_secret_access_key}"|' /etc/supervisor/conf.d/celeryd_webhook.conf
      - sed -i 's|^environment=.*|&SERVER_ID="'"${{SERVER_ID}}"'", ORG_CODE="{organization.uuid}", TPSTREAMS_URL="{settings.SITE_URL}", WASABI_ACCESS_KEY_ID="{organization.storage_access_key_id}", WASABI_BUCKET_REGION="{organization.storage_region}", WASABI_SECRET_ACCESS_KEY="{organization.storage_secret_access_key}"|' /etc/supervisor/conf.d/gunicorn.conf
      - systemctl enable supervisor
      - systemctl start supervisor
      - supervisorctl reread
      - supervisorctl update
      - supervisorctl start all
      - cd /home/<USER>
      - ./rclone.sh
    """


def _parse_live_stream_server_response(data):
    from app.models import LiveStream, LiveStreamUsage

    droplet = data.get("droplet")
    server_id = droplet.get("id")
    cost_per_hour = droplet.get("size", {}).get("price_hourly")
    ipv4_addresses = droplet["networks"]["v4"]
    public_ip = None

    for ip_info in ipv4_addresses:
        if ip_info.get("type") == "public":
            public_ip = ip_info.get("ip_address")
            break

    status = LiveStream.ServerStatus.CREATING
    if droplet.get("status") == "active":
        status = LiveStream.ServerStatus.CREATED
    elif droplet.get("status") == "new":
        status = LiveStream.ServerStatus.CREATING

    return LiveStreamServer(
        ip_address=public_ip,
        id=server_id,
        cost_per_hour=cost_per_hour,
        status=status,
        provider=LiveStreamUsage.ServerProvider.DIGITALOCEAN,
        private_ip_address=public_ip,
    )


def delete_digital_ocean_server(server_id):
    url = f"https://api.digitalocean.com/v2/droplets/{server_id}"
    response = requests.delete(url, headers=headers)
    response.raise_for_status()
