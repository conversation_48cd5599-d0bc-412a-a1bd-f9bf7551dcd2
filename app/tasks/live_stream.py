import datetime

import celery
import sentry_sdk
from django.conf import settings
from django.shortcuts import get_object_or_404
from django.template.loader import render_to_string
from django_multitenant.utils import (
    get_current_tenant,
    set_current_tenant,
    unset_current_tenant,
)

from app.domain.haproxy import remove_assigned_ip_address_from_proxy
from app.domain.live_stream import (
    create_live_stream_event,
    execute_queued_video_trim,
    is_transcoding_needed,
    verify_and_delete_server,
)
from app.domain.live_stream_usage import (
    update_daily_live_stream_usage,
    update_monthly_live_stream_usage,
)
from app.domain.local_file_to_storage_uploader import upload_local_files_to_storage
from app.domain.openresty import remove_ip_address_from_openresty
from app.models import LiveStream, LiveStreamEvent
from app.tasks.base import TpStreamsTask
from app.utils.file import delete_file, delete_folder, mkdir
from app.utils.ip_address import get_server_ip
from app.utils.s3 import delete_files, get_s3_config
from app.utils.storage import download_file
from app.utils.video import convert_fragmented_to_non_fragmented_video
from config.celery import app


class StopDisconnectedLiveStreamTask(TpStreamsTask):
    ignore_result = True

    def do_run(self, *args, **kwargs):
        from app.domain.live_stream import stop_live_stream
        from app.models import LiveStream

        try:
            live_stream_id = kwargs.get("live_stream_id")
            live_stream = get_object_or_404(LiveStream, id=live_stream_id)
            if (
                live_stream.status == LiveStream.Status.DISCONNECTED
                or live_stream.status == LiveStream.Status.NOT_STARTED
            ):
                stop_live_stream(live_stream)

                live_stream.termination_cause = (
                    LiveStream.TerminationCause.SCHEDULED_TERMINATION
                )

                live_stream.save(update_fields=["termination_cause"])
                create_live_stream_event(
                    live_stream,
                    LiveStreamEvent.Type.COMPLETED,
                    {"message": "StopDisconnectedLiveStreamTask completed"},
                )

        except Exception as e:
            sentry_sdk.capture_exception(e)
            create_live_stream_event(
                live_stream,
                LiveStreamEvent.Type.RECORDING,
                {"message": f"Error in StopDisconnectedLiveStreamTask: {str(e)}"},
            )


StopDisconnectedLiveStreamTask = app.register_task(StopDisconnectedLiveStreamTask())  # type: ignore


class DeleteLiveStreamServerTask(TpStreamsTask):
    ignore_result = True

    def do_run(self, *args, **kwargs):
        from app.domain.live_stream import delete_live_stream_server

        live_stream_id = kwargs.get("live_stream_id")
        live_stream = get_object_or_404(LiveStream, id=live_stream_id)
        if live_stream.status == LiveStream.Status.COMPLETED:
            delete_live_stream_server(live_stream)
            remove_ip_address_from_openresty(live_stream.asset)
            remove_assigned_ip_address_from_proxy(live_stream.asset)


DeleteLiveStreamServerTask = app.register_task(DeleteLiveStreamServerTask())  # type: ignore


class UploadLiveStreamRecordingTask(TpStreamsTask):
    ignore_result = True

    def do_run(self, *args, **kwargs):
        from app.domain.live_stream import transcode_live_stream
        from app.models import LiveStream

        try:
            live_stream = get_object_or_404(LiveStream, id=kwargs.get("live_stream_id"))
            self.log_task_entry_in_sentry(live_stream)
            self.store_task_server_details(live_stream)
            if live_stream.get_status_display() == "Recording":
                return
            self.update_status_as_recording(live_stream)
            self.download_recordings_to_local(live_stream)
            self.upload_files_to_storage(live_stream)
            transcode_live_stream(live_stream)
            live_stream.asset.notify_webhook()
            if not is_transcoding_needed(live_stream):
                try:
                    execute_queued_video_trim(live_stream.asset)
                except Exception as e:
                    sentry_sdk.capture_exception(e)
                verify_and_delete_server(live_stream)
            create_live_stream_event(
                live_stream,
                LiveStreamEvent.Type.COMPLETED,
                {"message": "UploadLiveStreamRecordingTask Completed"},
            )
        except Exception as e:
            sentry_sdk.capture_message(
                f"Error in UploadLiveStreamRecordingTask  Asset UUID - {live_stream.asset.uuid} Error - {e}",
                level="error",
            )
            create_live_stream_event(
                live_stream,
                LiveStreamEvent.Type.RECORDING,
                {
                    "message": f"Error in UploadLiveStreamRecordingTask - {live_stream.asset.uuid} Error - {e}"
                },
            )

    def log_task_entry_in_sentry(self, live_stream):
        """Logs the task entry to Sentry with relevant metadata."""
        try:
            with sentry_sdk.push_scope() as scope:
                scope.set_tag("org_code", live_stream.organization.uuid)
                scope.set_extra("asset_id", live_stream.asset.uuid)
                scope.set_extra("task_id", self.request.id)
                server_ip = get_server_ip()
                queue = self.request.delivery_info.get("routing_key")
                scope.set_extra("server_ip", server_ip)
                scope.set_extra("queue", queue)
                sentry_sdk.capture_message(
                    "UploadLiveStreamRecordingTask started", level="info"
                )
        except Exception as error:
            sentry_sdk.capture_message(
                f"UploadLiveStreamRecordingTask start log failed: {str(error)}",
                level="info",
            )

    def store_task_server_details(self, live_stream):
        try:
            server_ip = get_server_ip()
            queue = self.request.delivery_info.get("routing_key")
            live_stream.meta_data = live_stream.meta_data or {}
            live_stream.meta_data["upload_task"] = {
                "task_id": self.request.id,
                "server_ip": server_ip,
                "queue": queue,
            }
            live_stream.save(update_fields=["meta_data"])
            create_live_stream_event(
                live_stream,
                LiveStreamEvent.Type.RECORDING,
                {"message": "Stored task server details"},
            )
        except Exception as error:
            sentry_sdk.capture_exception(error)

    def update_status_as_recording(self, live_stream):
        live_stream.status = LiveStream.Status.RECORDING
        LiveStreamEvent.objects.create(
            type=LiveStreamEvent.Type.RECORDING,
            live_stream=live_stream,
            organization=live_stream.organization,
        )
        live_stream.save()

    def download_recordings_to_local(self, live_stream):
        local_folder_path = (
            f"{settings.BASE_DIR}/livestream_recordings/{live_stream.asset.uuid}"
        )
        mkdir(local_folder_path)
        base_recording_path = live_stream.get_hls_url.rsplit("/", 1)[0]

        download_file(
            f"{base_recording_path}/video.mp4",
            f"{local_folder_path}/video_frag.mp4",
        )
        create_live_stream_event(
            live_stream,
            LiveStreamEvent.Type.RECORDING,
            {"message": "Video downloaded."},
        )

        convert_fragmented_to_non_fragmented_video(
            f"{local_folder_path}/video_frag.mp4", f"{local_folder_path}/video.mp4"
        )
        create_live_stream_event(
            live_stream,
            LiveStreamEvent.Type.RECORDING,
            {"message": "Converted video format"},
        )

        delete_file(f"{local_folder_path}/video_frag.mp4")

        download_file(
            f"{base_recording_path}/ffmpeg_log.txt",
            f"{local_folder_path}/ffmpeg_log.txt",
        )
        create_live_stream_event(
            live_stream,
            LiveStreamEvent.Type.RECORDING,
            {"message": "ffmpeg log downloaded."},
        )

        download_file(
            f"{base_recording_path}/transmux_log.txt",
            f"{local_folder_path}/transmux_log.txt",
        )
        create_live_stream_event(
            live_stream,
            LiveStreamEvent.Type.RECORDING,
            {"message": "transmux log downloaded."},
        )

        if live_stream.enable_drm:
            download_file(
                f"{base_recording_path}/packager_widevine.txt",
                f"{local_folder_path}/packager_widevine.txt",
            )
            download_file(
                f"{base_recording_path}/packager_fairplay.txt",
                f"{local_folder_path}/packager_fairplay.txt",
            )
            create_live_stream_event(
                live_stream,
                LiveStreamEvent.Type.RECORDING,
                {"message": "DRM files downloaded successfully."},
            )

    def upload_files_to_storage(self, live_stream):
        local_folder_path = (
            f"{settings.BASE_DIR}/livestream_recordings/{live_stream.asset.uuid}"
        )
        local_files = [
            f"{local_folder_path}/video.mp4",
            f"{local_folder_path}/ffmpeg_log.txt",
            f"{local_folder_path}/transmux_log.txt",
        ]
        if live_stream.enable_drm:
            local_files.extend(
                [
                    f"{local_folder_path}/packager_widevine.txt",
                    f"{local_folder_path}/packager_fairplay.txt",
                ]
            )
        output_path = (
            f"/{self.organization.bucket_name}/private/{live_stream.asset.uuid}/"
        )

        upload_local_files_to_storage(local_files, output_path, self.organization)
        create_live_stream_event(
            live_stream,
            LiveStreamEvent.Type.RECORDING,
            {"message": "Files uploaded to storage successfully."},
        )
        self.delete_local_files(local_folder_path, live_stream)

    def delete_local_files(self, local_folder_path, live_stream):
        from app.domain.video import is_live_video_uploaded

        if is_live_video_uploaded(live_stream):
            delete_folder(local_folder_path)
            create_live_stream_event(
                live_stream,
                LiveStreamEvent.Type.RECORDING,
                {"message": "Local files deleted."},
            )


UploadLiveStreamRecordingTask = app.register_task(UploadLiveStreamRecordingTask())  # type: ignore


class DeleteLiveStreamFilesTask(TpStreamsTask):
    ignore_result = True

    def do_run(self, *args, **kwargs):
        live_stream = get_object_or_404(LiveStream, id=kwargs.get("live_stream_id"))

        s3_path = f"{settings.LIVE_STREAM_S3_BUCKET}/live_streams/{self.organization.uuid}/{live_stream.asset.uuid}/"
        config = get_s3_config(self.organization)
        delete_files(s3_path, config)


DeleteLiveStreamFilesTask = app.register_task(DeleteLiveStreamFilesTask())  # type: ignore


class UpdateLiveStreamUsageTask(celery.Task):
    def run(self, *args, **kwargs):
        date = datetime.date.today() - datetime.timedelta(days=1)
        self.update_live_stream_usage(date)

    def update_live_stream_usage(self, date):
        from app.models import Organization

        # Take a backup of current tenant
        current_tenant = get_current_tenant()
        unset_current_tenant()

        for organization in Organization.objects.all():
            set_current_tenant(organization)
            try:
                update_daily_live_stream_usage(organization, date)
                update_monthly_live_stream_usage(organization, date)
            except Exception as error:
                sentry_sdk.capture_exception(error)

        set_current_tenant(current_tenant)


UpdateLiveStreamUsageTask = app.register_task(UpdateLiveStreamUsageTask())  # type: ignore


class CheckLiveServerDeletionTask(TpStreamsTask):
    ignore_result = True

    def do_run(self, *args, **kwargs):
        live_stream_id = kwargs.get("live_stream_id")
        live_stream = get_object_or_404(LiveStream, id=live_stream_id)

        if live_stream.server_status != LiveStream.ServerStatus.DESTROYED:
            self.schedule_email_alert(live_stream)

    def schedule_email_alert(self, live_stream):
        from app.tasks.send_mail import send_email_task

        subject, message_text, message_html, to_emails = self.get_email_content(
            live_stream
        )
        if message_text:
            send_email_task.apply_async(
                kwargs={
                    "subject": subject,
                    "message_text": message_text,
                    "message_html": message_html,
                    "from_email": settings.DEFAULT_FROM_EMAIL,
                    "to_emails": to_emails,
                }
            )

    def get_email_content(self, live_stream):
        context = {
            "stream_title": live_stream.asset.title,
            "asset_id": live_stream.asset.uuid,
            "start_time": live_stream.start,
            "end_time": live_stream.end,
            "organization_id": live_stream.organization.uuid,
        }

        subject = render_to_string(
            "email/live_server_deletion_alert_subject.txt", context
        ).strip()
        message_text = render_to_string(
            "email/live_server_deletion_alert_mail.txt", context
        )
        message_html = render_to_string(
            "email/live_server_deletion_alert_mail.html", context
        )

        to_emails = settings.DEVELOPER_EMAILS
        return subject, message_text, message_html, to_emails


CheckLiveServerDeletionTask = app.register_task(CheckLiveServerDeletionTask())
