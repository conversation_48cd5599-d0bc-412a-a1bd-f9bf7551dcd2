import json
from datetime import datetime

import requests
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.db.models import Q
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from django.views.generic import (
    DeleteView,
    DetailView,
    ListView,
    TemplateView,
    UpdateView,
)
from django_filters.views import FilterView
from django_multitenant.utils import get_current_tenant

from app.domain.access_token import (
    get_or_create_access_token_without_validity,
    validate_access_token,
)
from app.domain.analytics import get_asset_views_analytics, get_metrics
from app.domain.cloud_storage import generate_presigned_url
from app.domain.subtitle import generate_subtitle, has_auto_generated_subtitle
from app.domain.video import (
    check_cdn_status,
    start_transcoding,
    transcode_video_with_backup,
)
from app.domain.zoom import trigger_zoom_recording_import_task
from app.filters.asset import AssetFilter
from app.forms import AssetForm, SubtitleForm, VideoPreferencesForm
from app.models import Asset
from app.models.access_token import AccessToken
from app.models.track import Track
from app.tasks.purge_asset import (
    delete_asset_task,
    empty_trash_task,
    restore_asset_task,
    soft_delete_asset_task,
)
from app.utils.datetime import get_month_end_date, get_month_start_date
from app.utils.http import AuthenticatedHttpRequest


@method_decorator(login_required, name="dispatch")
class AssetListView(ListView):
    template_name = "assets/videos/list.html"
    context_object_name = "assets"
    paginate_by = 20
    request: AuthenticatedHttpRequest

    def dispatch(self, request, *args, **kwargs):
        self.folder = None
        folder_uuid = request.GET.get("folder")
        if folder_uuid:
            self.folder = get_object_or_404(
                Asset.objects.filter(type=Asset.Type.FOLDER, uuid=folder_uuid)
            )
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        return (
            Asset.objects.filter(parent=self.folder)
            .prefetch_related("video", "video__organization", "live_stream")
            .order_by("-created")
            .sort_folders_first()
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        org_id = self.request.user.current_organization.uuid
        context.update(
            {
                "folder": self.folder,
                "is_empty": self.is_empty,
                "subtitle": f"GET {reverse('api:asset-list', kwargs={'organization_id': org_id, })}",
                "cta_text": "Upload",
                "cta_link": self.cta_link,
                "title": "Assets",
            }
        )
        return context

    @property
    def is_empty(self):
        return not self.get_queryset().exists()

    @property
    def cta_link(self):
        base_url = reverse("upload")
        if self.folder:
            return base_url + f"?folder={self.folder.uuid}"
        return base_url


@method_decorator(login_required, name="dispatch")
class AssetUploadView(TemplateView):
    template_name = "assets/videos/upload.html"

    def dispatch(self, request, *args, **kwargs):
        self.organization = get_current_tenant()
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["folder_uuid"] = self.request.GET.get("folder", "")
        context["enabled_resolutions"] = self.organization.enabled_resolutions or []
        return context


@method_decorator(login_required, name="dispatch")
class AssetMoveView(AssetListView):
    def dispatch(self, request, *args, **kwargs):
        self.asset = None
        asset_uuid = request.GET.get("asset")
        if asset_uuid:
            self.asset = get_object_or_404(Asset.objects.filter(uuid=asset_uuid))
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        return (
            Asset.objects.filter(type=Asset.Type.FOLDER, parent=self.folder)
            .prefetch_related("video", "video__organization")
            .order_by("-created")
            .exclude(uuid=self.asset.uuid)
            .sort_folders_first()
        )

    def post(self, request, *args, **kwargs):
        self.asset.move(self.folder)
        messages.success(self.request, "Asset has been moved successfully.")
        return redirect(self.redirect_url())

    def redirect_url(self):
        url = reverse("assets")
        if self.folder:
            url += f"?folder={self.folder.uuid}"
        return url

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(
            {
                "source": self.asset,
                "subtitle": "",
                "cta_text": "",
                "title": "Move Asset",
            }
        )
        return context


@method_decorator(login_required, name="dispatch")
class AssetDetailView(DetailView):
    template_name = "assets/videos/detail.html"
    slug_field = "uuid"
    slug_url_kwarg = "asset_uuid"

    def get_queryset(self):
        return Asset.objects.all().prefetch_related("video")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        org_id = self.object.organization.uuid  # type: ignore
        subtitle = f"GET {reverse('api:assets', kwargs={'organization_id': org_id, 'uuid': self.object.uuid})}"  # type: ignore # noqa
        context.update(
            {
                "embed_url": self.get_embed_url(),
                "subtitle": subtitle,
                "monthly_metrics_json": self.get_monthly_metrics_json(),
                "subtitle_form": SubtitleForm(initial={"video": self.object}),
                "active_captions": self.get_captions(is_active=True),
                "disabled_captions": self.get_captions(is_active=False),
                "is_video": True if hasattr(self.object, "video") else False,
                "asset_form": AssetForm(instance=self.object),
                "can_preview_video": self.can_preview_video(),
                "analytics_data": get_asset_views_analytics(self.object),
                "has_auto_generated_subtitle": has_auto_generated_subtitle(self.object),
            }
        )
        if hasattr(self.object, "video"):
            context.update(
                {
                    "video_form": VideoPreferencesForm(instance=self.object.video),
                }
            )
        return context

    def get_embed_url(self):
        access_token = get_or_create_access_token_without_validity(asset=self.object)
        embed_url = (
            f"{settings.SITE_URL}/embed/{self.object.organization.uuid}"  # type: ignore
            f"/{self.object.uuid}/?access_token={access_token.uuid}"
        )
        if self.request.GET.get("tpstreams_drm"):
            embed_url += "&tpstreams_drm=True"
        return embed_url

    def get_monthly_metrics_json(self):
        month = self.request.GET.get("month")
        return json.dumps(
            {
                str(key): value
                for key, value in self.get_asset_metrics_of_month(month).items()
            }
        )

    def get_asset_metrics_of_month(self, month, **kwargs):
        month_start_date = get_month_start_date(month)
        month_end_date = get_month_end_date(month)
        return get_metrics(self.object, month_start_date, month_end_date)

    def get_captions(self, is_active):
        if hasattr(self.object, "video"):
            return self.object.video.tracks.filter(
                is_active=is_active, type=Track.Type.SUBTITLE, url__isnull=False
            )

    def can_preview_video(self):
        if hasattr(self.object, "live_stream"):
            return True

        if hasattr(self.object, "video"):
            return self.object.video.get_status_display() == "Completed"


class AssetDebugView(TemplateView):
    template_name = "assets/videos/debug.html"

    def get_context_data(self, **kwargs):
        asset_uuid = kwargs.get("asset_uuid")
        asset = get_object_or_404(
            Asset,
            uuid=asset_uuid,
        )
        context = super().get_context_data(**kwargs)
        context.update(
            {
                "check_cdn_status": check_cdn_status(asset),
                "check_license_server": self.check_license_server(asset),
                "check_wasabi_status": self.check_wasabi_status(asset),
                "is_video": True if hasattr(asset, "video") else False,
                "check_haproxy_status": self.check_haproxy_status(asset),
                "check_live_server_status": self.check_live_server_status(asset),
            }
        )
        return context

    def post(self, request, *args, **kwargs):
        asset_uuid = kwargs.get("asset_uuid")
        asset = get_object_or_404(Asset, uuid=asset_uuid)
        access_key = request.POST.get("access_key")

        access_token_in_db = (
            AccessToken.objects.filter(uuid=access_key, asset=asset).first()
            if AccessToken.objects.filter(uuid=access_key, asset=asset).exists()
            else None
        )

        context = self.get_context_data(**kwargs)
        context.update(
            {
                "access_key": access_key,
                "access_token_in_cache": validate_access_token(asset, token=access_key),
                "access_token_in_db": access_token_in_db,
            }
        )

        return render(request, self.template_name, context)

    def check_license_server(self, asset):
        url = "https://license.tpstreams.com/accounts/login/?next=/"
        response = requests.head(url)
        print(response.status_code)
        return {"result": response.status_code == 200, "url": url}

    def check_wasabi_status(self, asset):
        from app.utils.browser import is_valid_url

        if hasattr(asset, "video"):
            url = generate_presigned_url(
                asset.organization, f"transcoded/{asset.uuid}/video.m3u8"
            )
            return {"result": is_valid_url(url), "url": url}
        return {"result": False}

    def check_haproxy_status(self, asset):
        if hasattr(asset, "live_stream"):
            url = (
                f"http://live.tpstreams.com/live/{asset.live_stream.organization.uuid}/"
                f"{asset.live_stream.asset.uuid}/video.m3u8"
            )
            if not asset.live_stream.get_server_status_display() == "Destroyed":
                response = requests.head(url)
                return {"result": response.status_code == 200, "url": url}
            return {"result": False, "url": url}

    def check_live_server_status(self, asset):
        if hasattr(asset, "live_stream"):
            url = (
                f"http://{asset.live_stream.server_ip}/live/{asset.live_stream.organization.uuid}/"
                f"{asset.live_stream.asset.uuid}/video.m3u8"
            )
            if not asset.live_stream.get_server_status_display() == "Destroyed":
                response = requests.head(url)
                return {"result": response.status_code == 200, "url": url}
            return {"result": False, "url": url}


@method_decorator(login_required, name="dispatch")
class AssetSearchView(FilterView, AssetListView):
    filterset_class = AssetFilter

    def get_queryset(self):
        queryset = (
            Asset.objects.prefetch_related("video", "video__organization")
            .order_by("-created")
            .sort_folders_first()
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        search_query = self.request.GET.get("q")
        title = (
            f"Search results for '{search_query}'" if search_query else "Search results"
        )
        context.update(
            {
                "subtitle": "",
                "cta_text": "",
                "title": title,
            }
        )
        return context


@login_required
def download_asset_source_video_view(request, asset_uuid):
    asset = get_object_or_404(Asset.objects.filter(uuid=asset_uuid))
    download_url = asset.get_download_url()
    if (
        download_url
        and not request.user.current_organization.uuid == "352dct"
        and not request.user.current_organization.uuid == "9mpasc"
    ):
        return redirect(download_url)

    messages.warning(request, _("This video is not downloadable."))
    return redirect(reverse("assets"))


@method_decorator(login_required, name="dispatch")
class AssetDeleteListView(AssetListView):
    template_name = "assets/videos/deleted_list.html"

    def dispatch(self, request, *args, **kwargs):
        self.asset = None
        asset_uuid = request.GET.get("asset")
        if asset_uuid:
            self.asset = get_object_or_404(
                Asset.objects.deleted_only().filter(
                    uuid=asset_uuid,
                    type__in=[
                        Asset.Type.VIDEO,
                        Asset.Type.LIVESTREAM,
                        Asset.Type.FOLDER,
                    ],
                )
            )
        return super().dispatch(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        action = request.POST.get("action")
        if action == "undelete":
            restore_asset_task.delay(self.asset.uuid, self.asset.organization.uuid)
            messages.success(self.request, "Asset restoration triggered successfully.")

        elif action == "delete":
            delete_asset_task.delay(self.asset.uuid)
            messages.success(self.request, "Asset deletion triggered successfully.")

        return redirect(reverse("deleted_assets"))

    def get_queryset(self):
        return (
            Asset.objects.deleted_only()
            .prefetch_related("video", "video__organization")
            .order_by("-deleted")
            .filter(
                Q(parent__isnull=True) | Q(parent__deleted__isnull=True),
                type__in=[Asset.Type.VIDEO, Asset.Type.LIVESTREAM, Asset.Type.FOLDER],
            )
            .sort_folders_first()
        )


@method_decorator(login_required, name="dispatch")
class EmptyTrashView(AssetListView):
    def post(self, request, *args, **kwargs):
        organization = self.request.user.current_organization
        empty_trash_task.delay(organization.uuid)
        messages.success(
            request, "Trash empty process has been triggered successfully."
        )
        return redirect(reverse("deleted_assets"))


@method_decorator(login_required, name="dispatch")
class AssetInternalsView(TemplateView):
    template_name = "assets/videos/internals.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        uuid = self.kwargs.get("uuid")
        organization = self.request.user.current_organization
        asset = get_object_or_404(Asset, uuid=uuid, organization=organization)
        context["asset"] = asset
        context["job_url"] = self.get_asset_job_url(asset)

        if asset.video.generate_subtitle:
            subtitle = asset.video.tracks.filter(subtitle_type=0).first()
            context["auto_genrated_subtitle"] = subtitle
            context["server_started_time"] = datetime.fromisoformat(
                subtitle.subtitle_data["SERVER_STARTED"]
            )
            context["subtitle_generated_time"] = datetime.fromisoformat(
                subtitle.subtitle_data["SUBTITLE_GENERATED"]
            )
            context["server_deleted_time"] = datetime.fromisoformat(
                subtitle.subtitle_data["SERVER_DELETED"]
            )

        return context

    def get_asset_job_url(self, asset):
        return f"https://lumberjack.testpress.in/admin/jobs/job/{asset.video.job_id}/"


@method_decorator(login_required, name="dispatch")
class AssetRetriggerView(TemplateView):
    template_name = "assets/videos/retrigger.html"

    def dispatch(self, request, *args, **kwargs):
        asset_uuid = kwargs.get("asset_uuid")
        self.asset = self._get_asset(asset_uuid)
        return super().dispatch(request, *args, **kwargs)

    def post(self, request, asset_uuid):
        self.video = self.asset.video

        if self._is_processing_video():
            messages.error(request, "Video Processing already in progress.")
            return redirect("asset_detail", asset_uuid=asset_uuid)

        action = request.POST.get("action")
        message = self._handle_action(action)
        messages.success(request, message)
        return redirect("asset_detail", asset_uuid=asset_uuid)

    def _get_asset(self, asset_uuid):
        return get_object_or_404(
            Asset, uuid=asset_uuid, organization=self.request.user.current_organization
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["asset"] = self.asset
        context["is_zoom_asset"] = hasattr(self.asset, "zoom_recording")
        return context

    def _is_processing_video(self):
        return self.video.status not in (
            self.video.Status.NOT_STARTED,
            self.video.Status.COMPLETED,
            self.video.Status.ERROR,
        )

    def _handle_action(self, action):
        if action == "retranscode":
            self.video.status = self.video.Status.NOT_STARTED
            start_transcoding(self.video)
            return "Video retriggered for transcoding."
        elif action == "regenerate_subtitles" and self.video.should_generate_subtitle():
            generate_subtitle(self.asset)
            return "Subtitle regeneration triggered successfully."
        elif action == "transcode_video_with_backup":
            transcode_video_with_backup(self.video)
            return "Transcoding with backup retriggered."
        elif action == "retrigger_import_zoom_recording":
            trigger_zoom_recording_import_task(self.asset.zoom_recording)
            return "Zoom recording import retriggered."


class AdvanceSettingsView(UpdateView):
    model = Asset
    form_class = AssetForm
    slug_field = "uuid"
    slug_url_kwarg = "asset_uuid"

    def get_success_url(self):
        return reverse("asset_detail", kwargs={"asset_uuid": self.object.uuid})


@method_decorator(login_required, name="dispatch")
class AssetDeleteSearchView(FilterView, AssetDeleteListView):
    filterset_class = AssetFilter

    def get_queryset(self):
        queryset = (
            Asset.objects.deleted_only()
            .prefetch_related("video", "video__organization")
            .order_by("-deleted")
        )

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        search_query = self.request.GET.get("q")
        title = (
            f"Search results for '{search_query}'" if search_query else "Search results"
        )
        context.update(
            {
                "subtitle": "",
                "cta_text": "",
                "title": title,
            }
        )
        return context


@method_decorator(login_required, name="dispatch")
class FolderDeleteView(DeleteView):
    def get_object(self, queryset=None):
        uuid = self.kwargs.get("uuid")
        return get_object_or_404(
            Asset.objects.filter(type=Asset.Type.FOLDER), uuid=uuid
        )

    def post(self, request, *args, **kwargs):
        folder = self.get_object()
        soft_delete_asset_task.delay(
            folder.uuid, self.request.user.current_organization.uuid
        )
        messages.success(
            self.request,
            "Folder deletion has been triggered. This may take a few moments and the folder will be moved to Trash.",
        )
        return redirect(self.redirect_url(folder))

    def redirect_url(self, folder):
        url = reverse("assets")
        if folder.parent:
            url += f"?folder={folder.parent.uuid}"
        return url
