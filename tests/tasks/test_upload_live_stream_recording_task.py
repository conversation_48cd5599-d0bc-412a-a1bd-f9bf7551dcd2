from unittest import mock

from app.models import LiveStream, LiveStreamEvent
from app.tasks.live_stream import UploadLiveStreamRecordingTask
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestUploadLiveStreamRecordingTask(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization(uuid="edee9b")

    @mock.patch("app.tasks.live_stream.download_file")
    @mock.patch("app.domain.live_stream.transcode_live_stream")
    @mock.patch("app.tasks.live_stream.upload_local_files_to_storage")
    @mock.patch("app.tasks.live_stream.convert_fragmented_to_non_fragmented_video")
    @mock.patch(
        "app.tasks.live_stream.UploadLiveStreamRecordingTask.delete_local_files"
    )
    def test_copy_files_to_wasabi_should_be_called(
        self,
        mock_delete_local_files,
        mock_convert_fragmented_to_non_fragmented_video,
        mock_upload_local_files_to_storage,
        mock_download_file,
        mock_transcode_live_stream,
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.DISCONNECTED,
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        UploadLiveStreamRecordingTask(
            live_stream_id=live_stream.id, organization_uuid=self.organization.uuid  # type: ignore
        )

        mock_upload_local_files_to_storage.assert_called()

    @mock.patch("app.tasks.live_stream.download_file")
    @mock.patch("app.domain.live_stream.transcode_live_stream")
    @mock.patch("app.tasks.live_stream.upload_local_files_to_storage")
    @mock.patch("app.tasks.live_stream.convert_fragmented_to_non_fragmented_video")
    @mock.patch(
        "app.tasks.live_stream.UploadLiveStreamRecordingTask.delete_local_files"
    )
    def test_upload_livestream_recording_task_should_save_recording_event(
        self,
        mock_delete_local_files,
        mock_convert_fragmented_to_non_fragmented_video,
        mock_upload_local_files_to_storage,
        mock_download_file,
        mock_transcode_live_stream,
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.DISCONNECTED,
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        UploadLiveStreamRecordingTask(
            live_stream_id=live_stream.id, organization_uuid=self.organization.uuid  # type: ignore
        )

        stop_event = LiveStreamEvent.objects.filter(
            type=LiveStreamEvent.Type.RECORDING,
            live_stream=live_stream,
            organization=live_stream.organization,
        ).first()

        self.assertEqual(stop_event.type, LiveStreamEvent.Type.RECORDING)
        self.assertEqual(stop_event.live_stream, live_stream)
        self.assertEqual(stop_event.organization, live_stream.organization)

    @mock.patch("app.tasks.live_stream.download_file")
    @mock.patch("app.domain.live_stream.transcode_live_stream")
    @mock.patch("app.tasks.live_stream.upload_local_files_to_storage")
    @mock.patch("app.tasks.live_stream.convert_fragmented_to_non_fragmented_video")
    @mock.patch(
        "app.tasks.live_stream.UploadLiveStreamRecordingTask.delete_local_files"
    )
    def test_upload_livestream_recording_task_should_create_events(
        self,
        mock_delete_local_files,
        mock_convert_fragmented_to_non_fragmented_video,
        mock_upload_local_files_to_storage,
        mock_download_file,
        mock_transcode_live_stream,
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.DISCONNECTED,
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )

        UploadLiveStreamRecordingTask(
            live_stream_id=live_stream.id, organization_uuid=self.organization.uuid  # type: ignore
        )

        events = LiveStreamEvent.objects.filter(
            live_stream=live_stream,
            organization=self.organization,
        )

        event_messages = [
            event.data["message"]
            for event in events
            if event.data and "message" in event.data
        ]
        self.assertIn("Files uploaded to storage successfully.", event_messages)
        self.assertIn("UploadLiveStreamRecordingTask Completed", event_messages)
